package channel

import (
	"net/http"

	"git.uozi.org/uozi/awm-api/api"
	"git.uozi.org/uozi/awm-api/internal/acl"
	internalChannel "git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/model/view"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

type APIRespChannel struct {
	Name      string            `json:"name"`
	RootID    int               `json:"root_id"`
	CurrentID int               `json:"current_id"`
	Children  []*APIRespChannel `json:"children"`
}

// 代理到internal层的读取范围函数
func channelReadScope(c *gin.Context) func(tx *gorm.DB) *gorm.DB {
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	return internalChannel.ChannelReadScope(
		user.ID,
		user.CanPrivileged(acl.Channel, acl.Read),
		privileged,
	)
}

// 检查渠道ID是否在用户的渠道树中
func isInChannelTree(c *gin.Context) bool {
	channelId := cast.ToUint64(c.Param("id"))
	user := api.CurrentUser(c)

	return internalChannel.IsUserInChannelTree(
		user.ID,
		channelId,
		user.CanPrivileged(acl.Channel, acl.Write),
	)
}

// GetChannel 获取渠道信息
func GetChannel(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))

	// 调用internal层获取渠道信息
	userChannel, err := internalChannel.GetChannelByID(id, channelReadScope(c))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, userChannel)
}

// ModifyChannel 修改渠道信息
func ModifyChannel(c *gin.Context) {
	if !isInChannelTree(c) {
		c.AbortWithError(http.StatusForbidden, internalChannel.ErrChannelNotInChannelTree)
		return
	}

	id := cast.ToUint64(c.Param("id"))

	// 调用internal层修改渠道信息
	err := internalChannel.ModifyChannelByID(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	GetChannel(c)
}

// GetChannelList 获取渠道列表
func GetChannelList(c *gin.Context) {
	channelQuery := c.Query("channel")
	user := api.CurrentUser(c)
	privileged := cast.ToBool(c.Query("privileged"))

	cosy.Core[model.Channel](c).
		SetTable(view.ChannelView).
		GormScope(func(tx *gorm.DB) *gorm.DB {
			if channelQuery != "" {
				tx.Where("name LIKE ?", "%"+channelQuery+"%")
			}

			if privileged && user.CanPrivileged(acl.Channel, acl.Read) {
				tx.Preload("ParentUser")
			} else {
				tx.Omit("relate_user_id", "root_user_id")
			}

			tx.
				Scopes(channelReadScope(c)).
				Joins(`
					JOIN (
						SELECT
							user_id AS uid,
							CASE
								WHEN MAX(id) = 0 THEN 0
								ELSE MAX(id)
							END AS max_id
						FROM channels_view
						WHERE user_deleted_at = 0
						AND status = ?
						AND (channel_type = ? OR channel_type = ?)
						GROUP BY user_id
					) AS t
					ON channels_view.user_id = t.uid
					AND (
						(channels_view.id = t.max_id AND t.max_id > 0) OR
						(channels_view.id = 0 AND t.max_id = 0)
					)
				`, model.UserStatusActive, model.ChannelTypeNormal, model.ChannelTypeDefault).
				Select("user_id, id, name, phone, email, status, root_user_id, parent_user_id")
			return tx
		}).
		SetScan(func(tx *gorm.DB) any {
			var channels []model.ChannelView
			tx.Find(&channels)
			return channels
		}).PagingList()
}

// CreateChannel 创建渠道关系
func CreateChannel(c *gin.Context) {
	if !isInChannelTree(c) {
		c.AbortWithError(http.StatusForbidden, internalChannel.ErrChannelNotInChannelTree)
		return
	}

	var json struct {
		RootUserID   uint64 `json:"root_user_id,string"`
		UserID       uint64 `json:"user_id,string"`
		RelateUserID uint64 `json:"relate_user_id,string"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	// 调用internal层创建渠道关系
	err := internalChannel.CreateChannelRelation(
		json.RootUserID,
		json.UserID,
		json.RelateUserID,
	)

	if err == internalChannel.ErrCircleSubordinate {
		c.JSON(http.StatusNotAcceptable, gin.H{
			"message": "circle subordinate",
		})
		return
	}

	if err == internalChannel.ErrChannelDuplicate {
		c.JSON(http.StatusNotAcceptable, gin.H{
			"message": "channel duplicate",
		})
		return
	}

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}

// DestroyChannelRelation 删除渠道关系
func DestroyChannelRelation(c *gin.Context) {
	var json struct {
		RootUserID   uint64 `json:"root_user_id,string"`
		RelateUserID uint64 `json:"relate_user_id,string"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	// 调用internal层删除渠道关系
	err := internalChannel.DestroyChannelRelation(
		json.RootUserID,
		json.RelateUserID,
	)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}
