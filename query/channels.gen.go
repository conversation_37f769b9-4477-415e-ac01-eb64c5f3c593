// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newChannel(db *gorm.DB, opts ...gen.DOOption) channel {
	_channel := channel{}

	_channel.channelDo.UseDB(db, opts...)
	_channel.channelDo.UseModel(&model.Channel{})

	tableName := _channel.channelDo.TableName()
	_channel.ALL = field.NewAsterisk(tableName)
	_channel.ID = field.NewUint64(tableName, "id")
	_channel.CreatedAt = field.NewUint64(tableName, "created_at")
	_channel.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_channel.DeletedAt = field.NewUint(tableName, "deleted_at")
	_channel.NodeUserID = field.NewUint64(tableName, "node_user_id")
	_channel.ParentUserID = field.NewUint64(tableName, "parent_user_id")
	_channel.TreeRootUserID = field.NewUint64(tableName, "tree_root_user_id")
	_channel.Level = field.NewUint(tableName, "level")
	_channel.NodePath = field.NewString(tableName, "node_path")
	_channel.NodeUser = channelBelongsToNodeUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("NodeUser", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("NodeUser.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("NodeUser.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("NodeUser.UserGroup", "model.UserGroup"),
		},
	}

	_channel.ParentUser = channelBelongsToParentUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ParentUser", "model.User"),
	}

	_channel.TreeRootUser = channelBelongsToTreeRootUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("TreeRootUser", "model.User"),
	}

	_channel.fillFieldMap()

	return _channel
}

type channel struct {
	channelDo

	ALL            field.Asterisk
	ID             field.Uint64
	CreatedAt      field.Uint64
	UpdatedAt      field.Uint64
	DeletedAt      field.Uint
	NodeUserID     field.Uint64 // 节点用户ID
	ParentUserID   field.Uint64 // 父节点用户ID
	TreeRootUserID field.Uint64 // 树根用户ID
	Level          field.Uint   // 节点层级
	NodePath       field.String // 节点路径
	NodeUser       channelBelongsToNodeUser

	ParentUser channelBelongsToParentUser

	TreeRootUser channelBelongsToTreeRootUser

	fieldMap map[string]field.Expr
}

func (c channel) Table(newTableName string) *channel {
	c.channelDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c channel) As(alias string) *channel {
	c.channelDo.DO = *(c.channelDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *channel) updateTableName(table string) *channel {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.NodeUserID = field.NewUint64(table, "node_user_id")
	c.ParentUserID = field.NewUint64(table, "parent_user_id")
	c.TreeRootUserID = field.NewUint64(table, "tree_root_user_id")
	c.Level = field.NewUint(table, "level")
	c.NodePath = field.NewString(table, "node_path")

	c.fillFieldMap()

	return c
}

func (c *channel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *channel) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 12)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["node_user_id"] = c.NodeUserID
	c.fieldMap["parent_user_id"] = c.ParentUserID
	c.fieldMap["tree_root_user_id"] = c.TreeRootUserID
	c.fieldMap["level"] = c.Level
	c.fieldMap["node_path"] = c.NodePath

}

func (c channel) clone(db *gorm.DB) channel {
	c.channelDo.ReplaceConnPool(db.Statement.ConnPool)
	c.NodeUser.db = db.Session(&gorm.Session{Initialized: true})
	c.NodeUser.db.Statement.ConnPool = db.Statement.ConnPool
	c.ParentUser.db = db.Session(&gorm.Session{Initialized: true})
	c.ParentUser.db.Statement.ConnPool = db.Statement.ConnPool
	c.TreeRootUser.db = db.Session(&gorm.Session{Initialized: true})
	c.TreeRootUser.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c channel) replaceDB(db *gorm.DB) channel {
	c.channelDo.ReplaceDB(db)
	c.NodeUser.db = db.Session(&gorm.Session{})
	c.ParentUser.db = db.Session(&gorm.Session{})
	c.TreeRootUser.db = db.Session(&gorm.Session{})
	return c
}

type channelBelongsToNodeUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a channelBelongsToNodeUser) Where(conds ...field.Expr) *channelBelongsToNodeUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelBelongsToNodeUser) WithContext(ctx context.Context) *channelBelongsToNodeUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelBelongsToNodeUser) Session(session *gorm.Session) *channelBelongsToNodeUser {
	a.db = a.db.Session(session)
	return &a
}

func (a channelBelongsToNodeUser) Model(m *model.Channel) *channelBelongsToNodeUserTx {
	return &channelBelongsToNodeUserTx{a.db.Model(m).Association(a.Name())}
}

func (a channelBelongsToNodeUser) Unscoped() *channelBelongsToNodeUser {
	a.db = a.db.Unscoped()
	return &a
}

type channelBelongsToNodeUserTx struct{ tx *gorm.Association }

func (a channelBelongsToNodeUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelBelongsToNodeUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelBelongsToNodeUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelBelongsToNodeUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelBelongsToNodeUserTx) Clear() error {
	return a.tx.Clear()
}

func (a channelBelongsToNodeUserTx) Count() int64 {
	return a.tx.Count()
}

func (a channelBelongsToNodeUserTx) Unscoped() *channelBelongsToNodeUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelBelongsToParentUser struct {
	db *gorm.DB

	field.RelationField
}

func (a channelBelongsToParentUser) Where(conds ...field.Expr) *channelBelongsToParentUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelBelongsToParentUser) WithContext(ctx context.Context) *channelBelongsToParentUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelBelongsToParentUser) Session(session *gorm.Session) *channelBelongsToParentUser {
	a.db = a.db.Session(session)
	return &a
}

func (a channelBelongsToParentUser) Model(m *model.Channel) *channelBelongsToParentUserTx {
	return &channelBelongsToParentUserTx{a.db.Model(m).Association(a.Name())}
}

func (a channelBelongsToParentUser) Unscoped() *channelBelongsToParentUser {
	a.db = a.db.Unscoped()
	return &a
}

type channelBelongsToParentUserTx struct{ tx *gorm.Association }

func (a channelBelongsToParentUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelBelongsToParentUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelBelongsToParentUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelBelongsToParentUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelBelongsToParentUserTx) Clear() error {
	return a.tx.Clear()
}

func (a channelBelongsToParentUserTx) Count() int64 {
	return a.tx.Count()
}

func (a channelBelongsToParentUserTx) Unscoped() *channelBelongsToParentUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelBelongsToTreeRootUser struct {
	db *gorm.DB

	field.RelationField
}

func (a channelBelongsToTreeRootUser) Where(conds ...field.Expr) *channelBelongsToTreeRootUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelBelongsToTreeRootUser) WithContext(ctx context.Context) *channelBelongsToTreeRootUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelBelongsToTreeRootUser) Session(session *gorm.Session) *channelBelongsToTreeRootUser {
	a.db = a.db.Session(session)
	return &a
}

func (a channelBelongsToTreeRootUser) Model(m *model.Channel) *channelBelongsToTreeRootUserTx {
	return &channelBelongsToTreeRootUserTx{a.db.Model(m).Association(a.Name())}
}

func (a channelBelongsToTreeRootUser) Unscoped() *channelBelongsToTreeRootUser {
	a.db = a.db.Unscoped()
	return &a
}

type channelBelongsToTreeRootUserTx struct{ tx *gorm.Association }

func (a channelBelongsToTreeRootUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelBelongsToTreeRootUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelBelongsToTreeRootUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelBelongsToTreeRootUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelBelongsToTreeRootUserTx) Clear() error {
	return a.tx.Clear()
}

func (a channelBelongsToTreeRootUserTx) Count() int64 {
	return a.tx.Count()
}

func (a channelBelongsToTreeRootUserTx) Unscoped() *channelBelongsToTreeRootUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c channelDo) FirstByID(id uint64) (result *model.Channel, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c channelDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update channels set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c channelDo) Debug() *channelDo {
	return c.withDO(c.DO.Debug())
}

func (c channelDo) WithContext(ctx context.Context) *channelDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c channelDo) ReadDB() *channelDo {
	return c.Clauses(dbresolver.Read)
}

func (c channelDo) WriteDB() *channelDo {
	return c.Clauses(dbresolver.Write)
}

func (c channelDo) Session(config *gorm.Session) *channelDo {
	return c.withDO(c.DO.Session(config))
}

func (c channelDo) Clauses(conds ...clause.Expression) *channelDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c channelDo) Returning(value interface{}, columns ...string) *channelDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c channelDo) Not(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c channelDo) Or(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c channelDo) Select(conds ...field.Expr) *channelDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c channelDo) Where(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c channelDo) Order(conds ...field.Expr) *channelDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c channelDo) Distinct(cols ...field.Expr) *channelDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c channelDo) Omit(cols ...field.Expr) *channelDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c channelDo) Join(table schema.Tabler, on ...field.Expr) *channelDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c channelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *channelDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c channelDo) RightJoin(table schema.Tabler, on ...field.Expr) *channelDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c channelDo) Group(cols ...field.Expr) *channelDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c channelDo) Having(conds ...gen.Condition) *channelDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c channelDo) Limit(limit int) *channelDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c channelDo) Offset(offset int) *channelDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c channelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *channelDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c channelDo) Unscoped() *channelDo {
	return c.withDO(c.DO.Unscoped())
}

func (c channelDo) Create(values ...*model.Channel) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c channelDo) CreateInBatches(values []*model.Channel, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c channelDo) Save(values ...*model.Channel) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c channelDo) First() (*model.Channel, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) Take() (*model.Channel, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) Last() (*model.Channel, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) Find() ([]*model.Channel, error) {
	result, err := c.DO.Find()
	return result.([]*model.Channel), err
}

func (c channelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Channel, err error) {
	buf := make([]*model.Channel, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c channelDo) FindInBatches(result *[]*model.Channel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c channelDo) Attrs(attrs ...field.AssignExpr) *channelDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c channelDo) Assign(attrs ...field.AssignExpr) *channelDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c channelDo) Joins(fields ...field.RelationField) *channelDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c channelDo) Preload(fields ...field.RelationField) *channelDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c channelDo) FirstOrInit() (*model.Channel, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) FirstOrCreate() (*model.Channel, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Channel), nil
	}
}

func (c channelDo) FindByPage(offset int, limit int) (result []*model.Channel, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c channelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c channelDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c channelDo) Delete(models ...*model.Channel) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *channelDo) withDO(do gen.Dao) *channelDo {
	c.DO = *do.(*gen.DO)
	return c
}
