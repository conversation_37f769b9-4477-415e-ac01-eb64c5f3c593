// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/awm-api/model"
)

func newChannelClosure(db *gorm.DB, opts ...gen.DOOption) channelClosure {
	_channelClosure := channelClosure{}

	_channelClosure.channelClosureDo.UseDB(db, opts...)
	_channelClosure.channelClosureDo.UseModel(&model.ChannelClosure{})

	tableName := _channelClosure.channelClosureDo.TableName()
	_channelClosure.ALL = field.NewAsterisk(tableName)
	_channelClosure.ID = field.NewUint64(tableName, "id")
	_channelClosure.CreatedAt = field.NewUint64(tableName, "created_at")
	_channelClosure.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_channelClosure.DeletedAt = field.NewUint(tableName, "deleted_at")
	_channelClosure.AncestorUserID = field.NewUint64(tableName, "ancestor_user_id")
	_channelClosure.DescendantUserID = field.NewUint64(tableName, "descendant_user_id")
	_channelClosure.PathLength = field.NewUint(tableName, "path_length")
	_channelClosure.TreeRootUserID = field.NewUint64(tableName, "tree_root_user_id")
	_channelClosure.AncestorUser = channelClosureBelongsToAncestorUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("AncestorUser", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("AncestorUser.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("AncestorUser.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("AncestorUser.UserGroup", "model.UserGroup"),
		},
	}

	_channelClosure.DescendantUser = channelClosureBelongsToDescendantUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DescendantUser", "model.User"),
	}

	_channelClosure.fillFieldMap()

	return _channelClosure
}

type channelClosure struct {
	channelClosureDo

	ALL              field.Asterisk
	ID               field.Uint64
	CreatedAt        field.Uint64
	UpdatedAt        field.Uint64
	DeletedAt        field.Uint
	AncestorUserID   field.Uint64 // 祖先节点用户ID
	DescendantUserID field.Uint64 // 后代节点用户ID
	PathLength       field.Uint   // 路径长度
	TreeRootUserID   field.Uint64 // 树根用户ID
	AncestorUser     channelClosureBelongsToAncestorUser

	DescendantUser channelClosureBelongsToDescendantUser

	fieldMap map[string]field.Expr
}

func (c channelClosure) Table(newTableName string) *channelClosure {
	c.channelClosureDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c channelClosure) As(alias string) *channelClosure {
	c.channelClosureDo.DO = *(c.channelClosureDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *channelClosure) updateTableName(table string) *channelClosure {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.AncestorUserID = field.NewUint64(table, "ancestor_user_id")
	c.DescendantUserID = field.NewUint64(table, "descendant_user_id")
	c.PathLength = field.NewUint(table, "path_length")
	c.TreeRootUserID = field.NewUint64(table, "tree_root_user_id")

	c.fillFieldMap()

	return c
}

func (c *channelClosure) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *channelClosure) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 10)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["ancestor_user_id"] = c.AncestorUserID
	c.fieldMap["descendant_user_id"] = c.DescendantUserID
	c.fieldMap["path_length"] = c.PathLength
	c.fieldMap["tree_root_user_id"] = c.TreeRootUserID

}

func (c channelClosure) clone(db *gorm.DB) channelClosure {
	c.channelClosureDo.ReplaceConnPool(db.Statement.ConnPool)
	c.AncestorUser.db = db.Session(&gorm.Session{Initialized: true})
	c.AncestorUser.db.Statement.ConnPool = db.Statement.ConnPool
	c.DescendantUser.db = db.Session(&gorm.Session{Initialized: true})
	c.DescendantUser.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c channelClosure) replaceDB(db *gorm.DB) channelClosure {
	c.channelClosureDo.ReplaceDB(db)
	c.AncestorUser.db = db.Session(&gorm.Session{})
	c.DescendantUser.db = db.Session(&gorm.Session{})
	return c
}

type channelClosureBelongsToAncestorUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a channelClosureBelongsToAncestorUser) Where(conds ...field.Expr) *channelClosureBelongsToAncestorUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelClosureBelongsToAncestorUser) WithContext(ctx context.Context) *channelClosureBelongsToAncestorUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelClosureBelongsToAncestorUser) Session(session *gorm.Session) *channelClosureBelongsToAncestorUser {
	a.db = a.db.Session(session)
	return &a
}

func (a channelClosureBelongsToAncestorUser) Model(m *model.ChannelClosure) *channelClosureBelongsToAncestorUserTx {
	return &channelClosureBelongsToAncestorUserTx{a.db.Model(m).Association(a.Name())}
}

func (a channelClosureBelongsToAncestorUser) Unscoped() *channelClosureBelongsToAncestorUser {
	a.db = a.db.Unscoped()
	return &a
}

type channelClosureBelongsToAncestorUserTx struct{ tx *gorm.Association }

func (a channelClosureBelongsToAncestorUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelClosureBelongsToAncestorUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelClosureBelongsToAncestorUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelClosureBelongsToAncestorUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelClosureBelongsToAncestorUserTx) Clear() error {
	return a.tx.Clear()
}

func (a channelClosureBelongsToAncestorUserTx) Count() int64 {
	return a.tx.Count()
}

func (a channelClosureBelongsToAncestorUserTx) Unscoped() *channelClosureBelongsToAncestorUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelClosureBelongsToDescendantUser struct {
	db *gorm.DB

	field.RelationField
}

func (a channelClosureBelongsToDescendantUser) Where(conds ...field.Expr) *channelClosureBelongsToDescendantUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a channelClosureBelongsToDescendantUser) WithContext(ctx context.Context) *channelClosureBelongsToDescendantUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a channelClosureBelongsToDescendantUser) Session(session *gorm.Session) *channelClosureBelongsToDescendantUser {
	a.db = a.db.Session(session)
	return &a
}

func (a channelClosureBelongsToDescendantUser) Model(m *model.ChannelClosure) *channelClosureBelongsToDescendantUserTx {
	return &channelClosureBelongsToDescendantUserTx{a.db.Model(m).Association(a.Name())}
}

func (a channelClosureBelongsToDescendantUser) Unscoped() *channelClosureBelongsToDescendantUser {
	a.db = a.db.Unscoped()
	return &a
}

type channelClosureBelongsToDescendantUserTx struct{ tx *gorm.Association }

func (a channelClosureBelongsToDescendantUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a channelClosureBelongsToDescendantUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a channelClosureBelongsToDescendantUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a channelClosureBelongsToDescendantUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a channelClosureBelongsToDescendantUserTx) Clear() error {
	return a.tx.Clear()
}

func (a channelClosureBelongsToDescendantUserTx) Count() int64 {
	return a.tx.Count()
}

func (a channelClosureBelongsToDescendantUserTx) Unscoped() *channelClosureBelongsToDescendantUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type channelClosureDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c channelClosureDo) FirstByID(id uint64) (result *model.ChannelClosure, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c channelClosureDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update channel_closures set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c channelClosureDo) Debug() *channelClosureDo {
	return c.withDO(c.DO.Debug())
}

func (c channelClosureDo) WithContext(ctx context.Context) *channelClosureDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c channelClosureDo) ReadDB() *channelClosureDo {
	return c.Clauses(dbresolver.Read)
}

func (c channelClosureDo) WriteDB() *channelClosureDo {
	return c.Clauses(dbresolver.Write)
}

func (c channelClosureDo) Session(config *gorm.Session) *channelClosureDo {
	return c.withDO(c.DO.Session(config))
}

func (c channelClosureDo) Clauses(conds ...clause.Expression) *channelClosureDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c channelClosureDo) Returning(value interface{}, columns ...string) *channelClosureDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c channelClosureDo) Not(conds ...gen.Condition) *channelClosureDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c channelClosureDo) Or(conds ...gen.Condition) *channelClosureDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c channelClosureDo) Select(conds ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c channelClosureDo) Where(conds ...gen.Condition) *channelClosureDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c channelClosureDo) Order(conds ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c channelClosureDo) Distinct(cols ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c channelClosureDo) Omit(cols ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c channelClosureDo) Join(table schema.Tabler, on ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c channelClosureDo) LeftJoin(table schema.Tabler, on ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c channelClosureDo) RightJoin(table schema.Tabler, on ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c channelClosureDo) Group(cols ...field.Expr) *channelClosureDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c channelClosureDo) Having(conds ...gen.Condition) *channelClosureDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c channelClosureDo) Limit(limit int) *channelClosureDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c channelClosureDo) Offset(offset int) *channelClosureDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c channelClosureDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *channelClosureDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c channelClosureDo) Unscoped() *channelClosureDo {
	return c.withDO(c.DO.Unscoped())
}

func (c channelClosureDo) Create(values ...*model.ChannelClosure) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c channelClosureDo) CreateInBatches(values []*model.ChannelClosure, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c channelClosureDo) Save(values ...*model.ChannelClosure) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c channelClosureDo) First() (*model.ChannelClosure, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelClosure), nil
	}
}

func (c channelClosureDo) Take() (*model.ChannelClosure, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelClosure), nil
	}
}

func (c channelClosureDo) Last() (*model.ChannelClosure, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelClosure), nil
	}
}

func (c channelClosureDo) Find() ([]*model.ChannelClosure, error) {
	result, err := c.DO.Find()
	return result.([]*model.ChannelClosure), err
}

func (c channelClosureDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ChannelClosure, err error) {
	buf := make([]*model.ChannelClosure, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c channelClosureDo) FindInBatches(result *[]*model.ChannelClosure, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c channelClosureDo) Attrs(attrs ...field.AssignExpr) *channelClosureDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c channelClosureDo) Assign(attrs ...field.AssignExpr) *channelClosureDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c channelClosureDo) Joins(fields ...field.RelationField) *channelClosureDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c channelClosureDo) Preload(fields ...field.RelationField) *channelClosureDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c channelClosureDo) FirstOrInit() (*model.ChannelClosure, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelClosure), nil
	}
}

func (c channelClosureDo) FirstOrCreate() (*model.ChannelClosure, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChannelClosure), nil
	}
}

func (c channelClosureDo) FindByPage(offset int, limit int) (result []*model.ChannelClosure, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c channelClosureDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c channelClosureDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c channelClosureDo) Delete(models ...*model.ChannelClosure) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *channelClosureDo) withDO(do gen.Dao) *channelClosureDo {
	c.DO = *do.(*gen.DO)
	return c
}
