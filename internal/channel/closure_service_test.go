package channel

import (
	"testing"

	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
	mysql "github.com/uozi-tech/cosy-driver-mysql"
	"github.com/uozi-tech/cosy/settings"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	// 初始化配置
	settings.Init("../../app.testing.ini")
	
	// 注册模型
	cosy.RegisterModels(model.GenerateAllModel()...)
	
	// 初始化数据库
	db := cosy.InitDB(mysql.Open(settings.DataBaseSettings))
	query.Init(db)
	model.Use(db)
	
	// 清理测试数据
	db.Exec("DELETE FROM channel_closures")
	db.Exec("DELETE FROM channels")
	
	return db
}

func TestClosureService_CreateNode(t *testing.T) {
	db := setupTestDB(t)
	service := &ClosureService{db: db}

	// 测试创建根节点
	err := service.CreateNode(1, 0)
	if err != nil {
		t.Fatalf("创建根节点失败: %v", err)
	}

	// 验证根节点
	var rootNode model.Channel
	if err := db.Where("node_user_id = ?", 1).First(&rootNode).Error; err != nil {
		t.Fatalf("查找根节点失败: %v", err)
	}

	if rootNode.ParentUserID != 0 {
		t.Errorf("根节点父ID应为0，实际为%d", rootNode.ParentUserID)
	}

	if rootNode.Level != 0 {
		t.Errorf("根节点层级应为0，实际为%d", rootNode.Level)
	}

	// 验证自引用闭包记录
	var selfClosure model.ChannelClosure
	if err := db.Where("ancestor_user_id = ? AND descendant_user_id = ? AND path_length = 0", 
		1, 1).First(&selfClosure).Error; err != nil {
		t.Fatalf("查找自引用闭包记录失败: %v", err)
	}

	// 测试创建子节点
	err = service.CreateNode(2, 1)
	if err != nil {
		t.Fatalf("创建子节点失败: %v", err)
	}

	// 验证子节点
	var childNode model.Channel
	if err := db.Where("node_user_id = ?", 2).First(&childNode).Error; err != nil {
		t.Fatalf("查找子节点失败: %v", err)
	}

	if childNode.ParentUserID != 1 {
		t.Errorf("子节点父ID应为1，实际为%d", childNode.ParentUserID)
	}

	if childNode.Level != 1 {
		t.Errorf("子节点层级应为1，实际为%d", childNode.Level)
	}

	// 验证祖先-后代闭包记录
	var ancestorClosure model.ChannelClosure
	if err := db.Where("ancestor_user_id = ? AND descendant_user_id = ? AND path_length = 1", 
		1, 2).First(&ancestorClosure).Error; err != nil {
		t.Fatalf("查找祖先闭包记录失败: %v", err)
	}

	t.Log("CreateNode 测试通过")
}

func TestClosureService_GetChildren(t *testing.T) {
	db := setupTestDB(t)
	service := &ClosureService{db: db}

	// 创建测试数据
	service.CreateNode(1, 0)  // 根节点
	service.CreateNode(2, 1)  // 子节点1
	service.CreateNode(3, 1)  // 子节点2
	service.CreateNode(4, 2)  // 孙节点

	// 测试获取直接子节点
	children, err := service.GetChildren(1)
	if err != nil {
		t.Fatalf("获取子节点失败: %v", err)
	}

	if len(children) != 2 {
		t.Errorf("期望2个直接子节点，实际%d个", len(children))
	}

	// 验证子节点ID
	childIDs := make([]uint64, len(children))
	for i, child := range children {
		childIDs[i] = child.NodeUserID
	}

	expectedIDs := []uint64{2, 3}
	for _, expectedID := range expectedIDs {
		found := false
		for _, actualID := range childIDs {
			if actualID == expectedID {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("未找到期望的子节点ID: %d", expectedID)
		}
	}

	t.Log("GetChildren 测试通过")
}

func TestClosureService_GetAllDescendants(t *testing.T) {
	db := setupTestDB(t)
	service := &ClosureService{db: db}

	// 创建测试数据
	service.CreateNode(1, 0)  // 根节点
	service.CreateNode(2, 1)  // 子节点1
	service.CreateNode(3, 1)  // 子节点2
	service.CreateNode(4, 2)  // 孙节点

	// 测试获取所有后代节点
	descendants, err := service.GetAllDescendants(1)
	if err != nil {
		t.Fatalf("获取后代节点失败: %v", err)
	}

	if len(descendants) != 3 {
		t.Errorf("期望3个后代节点，实际%d个", len(descendants))
	}

	// 验证后代节点ID
	descendantIDs := make([]uint64, len(descendants))
	for i, descendant := range descendants {
		descendantIDs[i] = descendant.NodeUserID
	}

	expectedIDs := []uint64{2, 3, 4}
	for _, expectedID := range expectedIDs {
		found := false
		for _, actualID := range descendantIDs {
			if actualID == expectedID {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("未找到期望的后代节点ID: %d", expectedID)
		}
	}

	t.Log("GetAllDescendants 测试通过")
}

func TestClosureService_DeleteNode(t *testing.T) {
	db := setupTestDB(t)
	service := &ClosureService{db: db}

	// 创建测试数据
	service.CreateNode(1, 0)  // 根节点
	service.CreateNode(2, 1)  // 子节点1
	service.CreateNode(3, 2)  // 孙节点1
	service.CreateNode(4, 2)  // 孙节点2

	// 删除中间节点2，其子节点应该重新连接到节点1
	err := service.DeleteNode(2)
	if err != nil {
		t.Fatalf("删除节点失败: %v", err)
	}

	// 验证节点2已被删除
	var deletedNode model.Channel
	if err := db.Where("node_user_id = ?", 2).First(&deletedNode).Error; err == nil {
		t.Error("节点2应该已被删除")
	}

	// 验证子节点重新连接到父节点
	var node3, node4 model.Channel
	if err := db.Where("node_user_id = ?", 3).First(&node3).Error; err != nil {
		t.Fatalf("查找节点3失败: %v", err)
	}
	if err := db.Where("node_user_id = ?", 4).First(&node4).Error; err != nil {
		t.Fatalf("查找节点4失败: %v", err)
	}

	if node3.ParentUserID != 1 {
		t.Errorf("节点3应该重新连接到节点1，实际父节点为%d", node3.ParentUserID)
	}
	if node4.ParentUserID != 1 {
		t.Errorf("节点4应该重新连接到节点1，实际父节点为%d", node4.ParentUserID)
	}

	// 验证闭包关系
	var closure model.ChannelClosure
	if err := db.Where("ancestor_user_id = ? AND descendant_user_id = ? AND path_length = 1", 
		1, 3).First(&closure).Error; err != nil {
		t.Error("节点1到节点3的闭包关系应该存在")
	}

	t.Log("DeleteNode 测试通过")
}

func TestClosureService_MoveNode(t *testing.T) {
	db := setupTestDB(t)
	service := &ClosureService{db: db}

	// 创建测试数据
	service.CreateNode(1, 0)  // 根节点
	service.CreateNode(2, 1)  // 子节点1
	service.CreateNode(3, 1)  // 子节点2
	service.CreateNode(4, 2)  // 孙节点

	// 将节点4从节点2移动到节点3
	err := service.MoveNode(4, 3)
	if err != nil {
		t.Fatalf("移动节点失败: %v", err)
	}

	// 验证节点4的新父节点
	var node4 model.Channel
	if err := db.Where("node_user_id = ?", 4).First(&node4).Error; err != nil {
		t.Fatalf("查找节点4失败: %v", err)
	}

	if node4.ParentUserID != 3 {
		t.Errorf("节点4应该移动到节点3下，实际父节点为%d", node4.ParentUserID)
	}

	// 验证新的闭包关系
	var closure model.ChannelClosure
	if err := db.Where("ancestor_user_id = ? AND descendant_user_id = ? AND path_length = 1", 
		3, 4).First(&closure).Error; err != nil {
		t.Error("节点3到节点4的闭包关系应该存在")
	}

	// 验证旧的闭包关系已删除
	if err := db.Where("ancestor_user_id = ? AND descendant_user_id = ? AND path_length = 1", 
		2, 4).First(&closure).Error; err == nil {
		t.Error("节点2到节点4的闭包关系应该已删除")
	}

	t.Log("MoveNode 测试通过")
}
