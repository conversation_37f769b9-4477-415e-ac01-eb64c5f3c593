package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"github.com/uozi-tech/cosy"
)

type Tree Node

// GetAllDescendantIDs 递归获取当前节点及其所有子节点的用户ID列表
func (t *Tree) GetAllDescendantIDs() []uint64 {
	// 将Tree转为Node，使用Node的方法
	return (*Node)(t).GetAllDescendantIDs()
}

// GetChannelTree 获取完整的渠道树结构
// userId: 用户ID，作为查询的起始点
// 返回该用户所在树的完整结构
func GetChannelTree(userId uint64) (c *Tree, err error) {
	closureService := NewClosureService()

	// 首先查找用户所在的节点
	var channel model.Channel
	db := cosy.UseDB()
	if err := db.Where("node_user_id = ?", userId).First(&channel).Error; err != nil {
		// 如果节点不存在，尝试创建
		if err := closureService.CreateNode(userId, 0); err != nil {
			return nil, err
		}
		// 重新查询
		if err := db.Where("node_user_id = ?", userId).First(&channel).Error; err != nil {
			return nil, err
		}
	}

	// 获取完整的树结构
	tree, err := closureService.GetTree(channel.TreeRootUserID)
	if err != nil {
		return nil, err
	}

	return (*Tree)(tree), nil
}

// GetChannelChildren 获取当前节点及其所有下级节点（包括间接下级）
// userId: 用户ID，作为查询的父节点
// 返回该用户及其所有下级节点构成的子树
func GetChannelChildren(userId uint64) (c *Tree, err error) {
	closureService := NewClosureService()

	// 获取子树结构
	tree, err := closureService.GetSubTree(userId)
	if err != nil {
		return nil, err
	}

	return (*Tree)(tree), nil
}

// 保留这些函数用于向后兼容，但现在使用闭包表服务实现

// getAllDescendantChannels 获取指定用户的所有下级渠道记录
func getAllDescendantChannels(userId uint64, visited map[uint64]bool) ([]*model.Channel, error) {
	closureService := NewClosureService()
	return closureService.GetAllDescendants(userId)
}
