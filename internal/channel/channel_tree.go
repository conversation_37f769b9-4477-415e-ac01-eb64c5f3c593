package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
)

type Tree Node

// GetAllDescendantIDs 递归获取当前节点及其所有子节点的用户ID列表
func (t *Tree) GetAllDescendantIDs() []uint64 {
	// 将Tree转为Node，使用Node的方法
	return (*Node)(t).GetAllDescendantIDs()
}

// GetChannelTree 获取完整的渠道树结构
// userId: 用户ID，作为查询的起始点
// 返回该用户所在树的完整结构
func GetChannelTree(userId uint64) (c *Tree, err error) {
	return getChannelTreeWithMode(userId, false)
}

// GetChannelChildren 获取当前节点及其所有下级节点（包括间接下级）
// userId: 用户ID，作为查询的父节点
// 返回该用户及其所有下级节点构成的子树
func GetChannelChildren(userId uint64) (c *Tree, err error) {
	return getChannelTreeWithMode(userId, true)
}

// getAllDescendantChannels 递归获取指定用户的所有下级渠道记录
func getAllDescendantChannels(userId uint64, visited map[uint64]bool) ([]*model.Channel, error) {
	if visited == nil {
		visited = make(map[uint64]bool)
	}

	// 防止循环引用
	if visited[userId] {
		return nil, nil
	}
	visited[userId] = true

	q := query.Channel

	// 查询直接下级
	directChildren, err := q.Where(q.UserID.Eq(userId)).
		Preload(q.RootUser, q.User, q.RelateUser).
		Find()
	if err != nil {
		return nil, err
	}

	var allChannels []*model.Channel
	allChannels = append(allChannels, directChildren...)

	// 递归查询间接下级
	for _, ch := range directChildren {
		if ch.RelateUserID > 0 {
			indirectChildren, err := getAllDescendantChannels(ch.RelateUserID, visited)
			if err != nil {
				return nil, err
			}
			allChannels = append(allChannels, indirectChildren...)
		}
	}

	return allChannels, nil
}

// getChannelTreeWithMode 根据不同模式获取渠道树
// userId: 用户ID
// onlyChildren: 是否只获取子节点树
// - true: 返回当前节点及其所有下级节点（包括间接下级）
// - false: 返回完整树结构
func getChannelTreeWithMode(userId uint64, onlyChildren bool) (c *Tree, err error) {
	q := query.Channel

	var chs []*model.Channel
	var user *model.User

	if onlyChildren {
		// 仅子节点模式：递归查询所有下级节点
		chs, err = getAllDescendantChannels(userId, nil)
		if err != nil {
			return nil, err
		}

		// 获取当前用户信息
		u := query.User
		user, err = u.FirstByID(userId)
		if err != nil {
			return nil, err
		}
	} else {
		// 完整树模式：查询整个树
		chs, _ = q.Where(q.RootUserID.Eq(userId)).
			Preload(q.RootUser, q.User, q.RelateUser).
			Find()

		// 如果没有找到记录，创建新的渠道记录
		if len(chs) == 0 {
			u := query.User

			user, err = u.FirstByID(userId)
			if err != nil {
				return nil, err
			}

			// 尝试查找当前用户是否有上级节点
			chnParent, _ := q.Where(q.RelateUserID.Eq(userId)).Preload(q.RootUser).First()

			rootID := userId

			var rootUser *model.User
			if chnParent != nil {
				rootID = chnParent.RootUserID
				rootUser = chnParent.RootUser
			}

			// 创建新的渠道记录
			chn := &model.Channel{
				RootUserID: rootID,
				RootUser:   rootUser,
				UserID:     userId,
				User:       user,
			}

			db := cosy.UseDB()

			// 存在则更新，不存在则创建
			db.Where("user_id", userId).FirstOrCreate(chn)

			// 重新查询整个树
			chs, _ = q.Where(q.RootUserID.Eq(chn.RootUserID)).
				Preload(q.RootUser, q.User, q.RelateUser).
				Find()

			userId = chn.RootUserID
		}
	}

	return buildChannelTree(userId, chs, user, onlyChildren)
}

// buildChannelTree 构建渠道树结构
func buildChannelTree(userId uint64, chs []*model.Channel, user *model.User, onlyChildren bool) (*Tree, error) {
	// 用于构建树的节点映射
	channelMap := make(map[uint64]*Node)

	if onlyChildren {
		// 仅子节点模式：构建当前节点作为根节点
		if user != nil {
			// 创建当前节点
			channelMap[userId] = &Node{
				Name:      user.Name,
				RootID:    userId, // 在仅子节点模式下，当前节点就是根节点
				CurrentID: userId,
				Children:  make([]*Node, 0),
			}
		}

		// 先创建所有节点
		for _, v := range chs {
			// 创建父节点（如果不存在）
			if v.User != nil && channelMap[v.UserID] == nil {
				channelMap[v.UserID] = &Node{
					Name:      v.User.Name,
					CurrentID: v.UserID,
					RootID:    userId, // 在仅子节点模式下，设置当前查询的userId为根
					Children:  make([]*Node, 0),
				}
			}

			// 创建子节点（如果不存在）
			if v.RelateUser != nil && channelMap[v.RelateUserID] == nil {
				channelMap[v.RelateUserID] = &Node{
					Name:      v.RelateUser.Name,
					CurrentID: v.RelateUserID,
					RootID:    userId, // 在仅子节点模式下，设置当前查询的userId为根
					Children:  make([]*Node, 0),
				}
			}
		}

		// 然后构建父子关系
		for _, v := range chs {
			if v.RelateUser != nil && channelMap[v.UserID] != nil && channelMap[v.RelateUserID] != nil {
				// 将子节点添加到父节点的子节点列表中
				channelMap[v.UserID].Children = append(channelMap[v.UserID].Children, channelMap[v.RelateUserID])
			}
		}
	} else {
		// 完整树模式：先创建所有节点
		for _, v := range chs {
			if v.User != nil && channelMap[v.UserID] == nil {
				channelMap[v.UserID] = &Node{
					Name:      v.User.Name,
					RootID:    v.RootUserID,
					CurrentID: v.UserID,
					Children:  make([]*Node, 0),
				}
			}
		}

		// 然后构建节点之间的父子关系
		for _, v := range chs {
			if v.RelateUser != nil {
				// 创建关联用户节点（如果不存在）
				if channelMap[v.RelateUserID] == nil {
					channelMap[v.RelateUserID] = &Node{
						Name:      v.RelateUser.Name,
						CurrentID: v.RelateUserID,
						RootID:    v.RootUserID,
						Children:  make([]*Node, 0),
					}
				}

				// 构建父子关系：将关联用户节点添加为当前节点的子节点
				if channelMap[v.UserID] != nil {
					channelMap[v.UserID].Children = append(channelMap[v.UserID].Children, channelMap[v.RelateUserID])
				}
			}
		}
	}

	return (*Tree)(channelMap[userId]), nil
}
