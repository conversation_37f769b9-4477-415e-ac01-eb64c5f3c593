package channel

import (
	"git.uozi.org/uozi/awm-api/model"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// ChannelReadScope 返回一个GORM查询范围函数，用于限制用户只能查看自己及下级渠道的数据
// 如果用户有特权且请求包含privileged参数，则不限制查询范围
func ChannelReadScope(userID uint64, canPrivileged bool, privileged bool) func(tx *gorm.DB) *gorm.DB {
	if privileged && canPrivileged {
		return func(tx *gorm.DB) *gorm.DB {
			return tx
		}
	}

	return func(tx *gorm.DB) *gorm.DB {
		// 获取当前用户及其所有下级节点的ID列表
		tree, err := GetChannelChildren(userID)
		userIds := []uint64{}

		if err == nil && tree != nil {
			// 递归获取所有下级节点的ID
			allUserIds := tree.GetAllDescendantIDs()
			if len(allUserIds) > 0 {
				userIds = allUserIds
			}
		}

		return tx.
			Where("user_id IN ?", userIds).
			Where("user_id != ?", userID).
			Where("channel_type = ? OR channel_type = ?", model.ChannelTypeDefault, model.ChannelTypeNormal)
	}
}

// IsUserInChannelTree 检查指定的渠道ID是否在用户的渠道树中
func IsUserInChannelTree(userID uint64, channelID uint64, hasGlobalPermission bool) bool {
	// 如果有全局权限，则直接返回true
	if hasGlobalPermission {
		return true
	}

	var ids []uint64
	// 获取渠道树
	tree, err := GetChannelTree(userID)
	if err != nil {
		ids = append(ids, userID)
	} else {
		// 获取当前用户及其所有下级节点的ID
		ids = tree.GetAllDescendantIDs()
	}

	// 判断请求的渠道是否在树中
	return lo.Contains(ids, channelID)
}

// GetChannelByID 根据ID获取渠道信息
func GetChannelByID(id uint64, scope func(tx *gorm.DB) *gorm.DB) (*model.User, error) {
	userChannel := &model.User{}
	db := cosy.UseDB()

	err := db.Model(&model.User{}).
		Scopes(scope).
		Joins("LEFT JOIN channels ON channels.user_id = users.id").
		Where("users.id = ?", id).
		Scan(userChannel).
		Error

	return userChannel, err
}

// ModifyChannelByID 更新渠道信息
func ModifyChannelByID(id uint64) error {
	closureService := NewClosureService()

	// 检查节点是否存在，如果不存在则创建
	db := cosy.UseDB()
	var channel model.Channel
	if err := db.Where("node_user_id = ?", id).First(&channel).Error; err != nil {
		// 节点不存在，创建为根节点
		return closureService.CreateNode(id, 0)
	}

	// 节点已存在，无需修改
	return nil
}

// CreateChannelRelation 创建渠道关系
func CreateChannelRelation(rootUserID uint64, userID uint64, relateUserID uint64) error {
	// 防止循环路径
	if userID == relateUserID {
		return ErrCircleSubordinate
	}

	closureService := NewClosureService()

	// 检查子节点是否已经有父节点
	db := cosy.UseDB()
	var existingChannel model.Channel
	if err := db.Where("node_user_id = ?", relateUserID).First(&existingChannel).Error; err == nil {
		if existingChannel.ParentUserID > 0 {
			return ErrChannelDuplicate
		}
	}

	// 创建节点关系
	return closureService.CreateNode(relateUserID, userID)
}

// DestroyChannelRelation 删除渠道关系
func DestroyChannelRelation(rootUserID uint64, relateUserID uint64) error {
	closureService := NewClosureService()

	// 检查节点是否存在
	db := cosy.UseDB()
	var nodeToDelete model.Channel
	if err := db.Where("node_user_id = ?", relateUserID).First(&nodeToDelete).Error; err != nil {
		return err
	}

	// 不允许删除根节点
	if nodeToDelete.ParentUserID == 0 {
		return ErrCannotRemoveRootNode
	}

	// 删除节点（子节点会自动重新连接到父节点）
	return closureService.DeleteNode(relateUserID)
}
