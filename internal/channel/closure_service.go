package channel

import (
	"fmt"
	"strconv"
	"strings"

	"git.uozi.org/uozi/awm-api/model"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// ClosureService 闭包表服务
type ClosureService struct {
	db *gorm.DB
}

// NewClosureService 创建闭包表服务实例
func NewClosureService() *ClosureService {
	return &ClosureService{
		db: cosy.UseDB(),
	}
}

// CreateNode 创建新节点
func (s *ClosureService) CreateNode(nodeUserID, parentUserID uint64) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 1. 创建节点记录
		var parentNode *model.Channel
		var level uint = 0
		var treeRootUserID uint64 = nodeUserID
		var nodePath string = strconv.FormatUint(nodeUserID, 10)

		if parentUserID > 0 {
			// 查找父节点信息
			if err := tx.Where("node_user_id = ?", parentUserID).First(&parentNode).Error; err != nil {
				return fmt.Errorf("父节点不存在: %w", err)
			}
			level = parentNode.Level + 1
			treeRootUserID = parentNode.TreeRootUserID
			nodePath = parentNode.NodePath + "/" + strconv.FormatUint(nodeUserID, 10)
		}

		// 创建节点
		node := &model.Channel{
			NodeUserID:     nodeUserID,
			ParentUserID:   parentUserID,
			TreeRootUserID: treeRootUserID,
			Level:          level,
			NodePath:       nodePath,
		}

		if err := tx.Create(node).Error; err != nil {
			return fmt.Errorf("创建节点失败: %w", err)
		}

		// 2. 创建闭包表记录
		// 首先创建自己到自己的记录
		selfClosure := &model.ChannelClosure{
			AncestorUserID:   nodeUserID,
			DescendantUserID: nodeUserID,
			PathLength:       0,
			TreeRootUserID:   treeRootUserID,
		}
		if err := tx.Create(selfClosure).Error; err != nil {
			return fmt.Errorf("创建自引用闭包记录失败: %w", err)
		}

		// 如果有父节点，创建所有祖先到当前节点的闭包记录
		if parentUserID > 0 {
			// 查询父节点的所有祖先
			var ancestorClosures []model.ChannelClosure
			if err := tx.Where("descendant_user_id = ? AND tree_root_user_id = ?",
				parentUserID, treeRootUserID).Find(&ancestorClosures).Error; err != nil {
				return fmt.Errorf("查询祖先节点失败: %w", err)
			}

			// 为每个祖先创建到当前节点的闭包记录
			for _, ancestorClosure := range ancestorClosures {
				newClosure := &model.ChannelClosure{
					AncestorUserID:   ancestorClosure.AncestorUserID,
					DescendantUserID: nodeUserID,
					PathLength:       ancestorClosure.PathLength + 1,
					TreeRootUserID:   treeRootUserID,
				}
				if err := tx.Create(newClosure).Error; err != nil {
					return fmt.Errorf("创建祖先闭包记录失败: %w", err)
				}
			}
		}

		return nil
	})
}

// DeleteNode 删除节点（将子节点重新连接到父节点）
func (s *ClosureService) DeleteNode(nodeUserID uint64) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 1. 查找要删除的节点
		var node model.Channel
		if err := tx.Where("node_user_id = ?", nodeUserID).First(&node).Error; err != nil {
			return fmt.Errorf("节点不存在: %w", err)
		}

		// 2. 查找直接子节点
		var children []model.Channel
		if err := tx.Where("parent_user_id = ?", nodeUserID).Find(&children).Error; err != nil {
			return fmt.Errorf("查询子节点失败: %w", err)
		}

		// 3. 将子节点重新连接到父节点
		for _, child := range children {
			if err := s.moveNodeToNewParent(tx, child.NodeUserID, node.ParentUserID); err != nil {
				return fmt.Errorf("重新连接子节点失败: %w", err)
			}
		}

		// 4. 删除与该节点相关的所有闭包记录
		if err := tx.Where("ancestor_user_id = ? OR descendant_user_id = ?",
			nodeUserID, nodeUserID).Delete(&model.ChannelClosure{}).Error; err != nil {
			return fmt.Errorf("删除闭包记录失败: %w", err)
		}

		// 5. 删除节点记录
		if err := tx.Where("node_user_id = ?", nodeUserID).Delete(&model.Channel{}).Error; err != nil {
			return fmt.Errorf("删除节点失败: %w", err)
		}

		return nil
	})
}

// MoveNode 移动节点到新的父节点
func (s *ClosureService) MoveNode(nodeUserID, newParentUserID uint64) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		return s.moveNodeToNewParent(tx, nodeUserID, newParentUserID)
	})
}

// moveNodeToNewParent 内部方法：移动节点到新父节点
func (s *ClosureService) moveNodeToNewParent(tx *gorm.DB, nodeUserID, newParentUserID uint64) error {
	// 1. 查找当前节点
	var node model.Channel
	if err := tx.Where("node_user_id = ?", nodeUserID).First(&node).Error; err != nil {
		return fmt.Errorf("节点不存在: %w", err)
	}

	// 2. 查找新父节点信息
	var newParent *model.Channel
	var newLevel uint = 0
	var newTreeRootUserID uint64 = nodeUserID
	var newNodePath string = strconv.FormatUint(nodeUserID, 10)

	if newParentUserID > 0 {
		newParent = &model.Channel{}
		if err := tx.Where("node_user_id = ?", newParentUserID).First(newParent).Error; err != nil {
			return fmt.Errorf("新父节点不存在: %w", err)
		}
		newLevel = newParent.Level + 1
		newTreeRootUserID = newParent.TreeRootUserID
		newNodePath = newParent.NodePath + "/" + strconv.FormatUint(nodeUserID, 10)
	}

	// 3. 获取当前节点的所有后代
	var descendants []model.ChannelClosure
	if err := tx.Where("ancestor_user_id = ?", nodeUserID).Find(&descendants).Error; err != nil {
		return fmt.Errorf("查询后代节点失败: %w", err)
	}

	// 4. 删除当前节点及其后代的所有旧闭包关系
	descendantUserIDs := make([]uint64, len(descendants))
	for i, desc := range descendants {
		descendantUserIDs[i] = desc.DescendantUserID
	}

	if err := tx.Where("descendant_user_id IN ?", descendantUserIDs).Delete(&model.ChannelClosure{}).Error; err != nil {
		return fmt.Errorf("删除旧闭包关系失败: %w", err)
	}

	// 5. 更新节点信息
	levelDiff := int(newLevel) - int(node.Level)
	if err := tx.Model(&node).Updates(map[string]interface{}{
		"parent_user_id":    newParentUserID,
		"tree_root_user_id": newTreeRootUserID,
		"level":             newLevel,
		"node_path":         newNodePath,
	}).Error; err != nil {
		return fmt.Errorf("更新节点信息失败: %w", err)
	}

	// 6. 重新创建闭包关系
	// 首先为每个后代节点创建自引用记录
	for _, desc := range descendants {
		selfClosure := &model.ChannelClosure{
			AncestorUserID:   desc.DescendantUserID,
			DescendantUserID: desc.DescendantUserID,
			PathLength:       0,
			TreeRootUserID:   newTreeRootUserID,
		}
		if err := tx.Create(selfClosure).Error; err != nil {
			return fmt.Errorf("创建自引用闭包记录失败: %w", err)
		}

		// 更新后代节点的层级和路径
		var descendantNode model.Channel
		if err := tx.Where("node_user_id = ?", desc.DescendantUserID).First(&descendantNode).Error; err != nil {
			continue
		}

		newDescendantLevel := uint(int(descendantNode.Level) + levelDiff)
		pathParts := strings.Split(descendantNode.NodePath, "/")
		newDescendantPath := newNodePath
		if len(pathParts) > 1 {
			newDescendantPath = newNodePath + "/" + strings.Join(pathParts[1:], "/")
		}

		if err := tx.Model(&descendantNode).Updates(map[string]interface{}{
			"tree_root_user_id": newTreeRootUserID,
			"level":             newDescendantLevel,
			"node_path":         newDescendantPath,
		}).Error; err != nil {
			return fmt.Errorf("更新后代节点信息失败: %w", err)
		}
	}

	// 7. 创建新的祖先-后代关系
	if newParentUserID > 0 {
		// 查询新父节点的所有祖先
		var newAncestors []model.ChannelClosure
		if err := tx.Where("descendant_user_id = ?", newParentUserID).Find(&newAncestors).Error; err != nil {
			return fmt.Errorf("查询新祖先节点失败: %w", err)
		}

		// 为每个祖先和每个后代创建闭包记录
		for _, ancestor := range newAncestors {
			for _, desc := range descendants {
				newClosure := &model.ChannelClosure{
					AncestorUserID:   ancestor.AncestorUserID,
					DescendantUserID: desc.DescendantUserID,
					PathLength:       ancestor.PathLength + desc.PathLength + 1,
					TreeRootUserID:   newTreeRootUserID,
				}
				if err := tx.Create(newClosure).Error; err != nil {
					return fmt.Errorf("创建新闭包记录失败: %w", err)
				}
			}
		}
	}

	return nil
}

// GetChildren 获取直接子节点
func (s *ClosureService) GetChildren(nodeUserID uint64) ([]*model.Channel, error) {
	var children []*model.Channel
	err := s.db.Where("parent_user_id = ?", nodeUserID).
		Preload("NodeUser").
		Find(&children).Error
	return children, err
}

// GetAllDescendants 获取所有后代节点
func (s *ClosureService) GetAllDescendants(nodeUserID uint64) ([]*model.Channel, error) {
	var descendants []*model.Channel
	err := s.db.Table("channels").
		Joins("JOIN channel_closures ON channels.node_user_id = channel_closures.descendant_user_id").
		Where("channel_closures.ancestor_user_id = ? AND channel_closures.path_length > 0", nodeUserID).
		Preload("NodeUser").
		Find(&descendants).Error
	return descendants, err
}

// GetAncestors 获取所有祖先节点
func (s *ClosureService) GetAncestors(nodeUserID uint64) ([]*model.Channel, error) {
	var ancestors []*model.Channel
	err := s.db.Table("channels").
		Joins("JOIN channel_closures ON channels.node_user_id = channel_closures.ancestor_user_id").
		Where("channel_closures.descendant_user_id = ? AND channel_closures.path_length > 0", nodeUserID).
		Order("channel_closures.path_length ASC").
		Preload("NodeUser").
		Find(&ancestors).Error
	return ancestors, err
}

// GetTree 获取完整的树结构
func (s *ClosureService) GetTree(rootUserID uint64) (*Node, error) {
	// 获取树中的所有节点
	var channels []*model.Channel
	err := s.db.Where("tree_root_user_id = ?", rootUserID).
		Preload("NodeUser").
		Order("level ASC, node_user_id ASC").
		Find(&channels).Error
	if err != nil {
		return nil, err
	}

	if len(channels) == 0 {
		return nil, fmt.Errorf("未找到树节点")
	}

	// 构建节点映射
	nodeMap := make(map[uint64]*Node)
	var root *Node

	// 创建所有节点
	for _, ch := range channels {
		node := &Node{
			Name:      ch.NodeUser.Name,
			RootID:    ch.TreeRootUserID,
			CurrentID: ch.NodeUserID,
			Children:  make([]*Node, 0),
		}
		nodeMap[ch.NodeUserID] = node

		if ch.ParentUserID == 0 {
			root = node
		}
	}

	// 构建父子关系
	for _, ch := range channels {
		if ch.ParentUserID > 0 {
			if parent, exists := nodeMap[ch.ParentUserID]; exists {
				if child, exists := nodeMap[ch.NodeUserID]; exists {
					parent.Children = append(parent.Children, child)
				}
			}
		}
	}

	return root, nil
}

// GetSubTree 获取子树结构（从指定节点开始）
func (s *ClosureService) GetSubTree(nodeUserID uint64) (*Node, error) {
	// 获取该节点及其所有后代
	var channels []*model.Channel
	err := s.db.Table("channels").
		Joins("JOIN channel_closures ON channels.node_user_id = channel_closures.descendant_user_id").
		Where("channel_closures.ancestor_user_id = ?", nodeUserID).
		Preload("NodeUser").
		Order("channel_closures.path_length ASC, channels.node_user_id ASC").
		Find(&channels).Error
	if err != nil {
		return nil, err
	}

	if len(channels) == 0 {
		return nil, fmt.Errorf("未找到节点")
	}

	// 构建节点映射
	nodeMap := make(map[uint64]*Node)
	var root *Node

	// 创建所有节点
	for _, ch := range channels {
		node := &Node{
			Name:      ch.NodeUser.Name,
			RootID:    nodeUserID, // 在子树模式下，根节点是查询的起始节点
			CurrentID: ch.NodeUserID,
			Children:  make([]*Node, 0),
		}
		nodeMap[ch.NodeUserID] = node

		if ch.NodeUserID == nodeUserID {
			root = node
		}
	}

	// 构建父子关系
	for _, ch := range channels {
		if ch.ParentUserID > 0 {
			if parent, exists := nodeMap[ch.ParentUserID]; exists {
				if child, exists := nodeMap[ch.NodeUserID]; exists {
					parent.Children = append(parent.Children, child)
				}
			}
		}
	}

	return root, nil
}
