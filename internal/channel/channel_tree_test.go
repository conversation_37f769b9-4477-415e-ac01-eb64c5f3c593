package channel

import (
	"testing"
	"git.uozi.org/uozi/awm-api/model"
)

// TestGetAllDescendantChannels 测试递归获取所有下级渠道记录
func TestGetAllDescendantChannels(t *testing.T) {
	// 这里需要根据实际的数据库连接和测试数据来编写测试
	// 由于涉及数据库操作，建议在集成测试中进行
	t.<PERSON>("需要数据库连接的集成测试")
}

// TestBuildChannelTree 测试构建渠道树结构
func TestBuildChannelTree(t *testing.T) {
	// 模拟测试数据
	testChannels := []*model.Channel{
		{
			UserID:       1,
			User:         &model.User{Name: "用户1"},
			RelateUserID: 2,
			RelateUser:   &model.User{Name: "用户2"},
			RootUserID:   1,
		},
		{
			UserID:       2,
			User:         &model.User{Name: "用户2"},
			RelateUserID: 3,
			RelateUser:   &model.User{Name: "用户3"},
			RootUserID:   1,
		},
		{
			UserID:       2,
			User:         &model.User{Name: "用户2"},
			RelateUserID: 4,
			RelateUser:   &model.User{Name: "用户4"},
			RootUserID:   1,
		},
	}

	testUser := &model.User{Name: "用户1"}

	// 测试 onlyChildren 模式
	tree, err := buildChannelTree(1, testChannels, testUser, true)
	if err != nil {
		t.Fatalf("构建渠道树失败: %v", err)
	}

	if tree == nil {
		t.Fatal("返回的树为空")
	}

	// 验证根节点
	if tree.CurrentID != 1 {
		t.Errorf("期望根节点ID为1，实际为%d", tree.CurrentID)
	}

	if tree.Name != "用户1" {
		t.Errorf("期望根节点名称为'用户1'，实际为'%s'", tree.Name)
	}

	// 验证子节点数量
	if len(tree.Children) != 1 {
		t.Errorf("期望根节点有1个直接子节点，实际有%d个", len(tree.Children))
	}

	// 验证第一级子节点
	if len(tree.Children) > 0 {
		child := tree.Children[0]
		if child.CurrentID != 2 {
			t.Errorf("期望第一个子节点ID为2，实际为%d", child.CurrentID)
		}

		// 验证第二级子节点
		if len(child.Children) != 2 {
			t.Errorf("期望第一个子节点有2个子节点，实际有%d个", len(child.Children))
		}
	}

	t.Log("渠道树构建测试通过")
}

// TestChannelTreeMethods 测试树的方法
func TestChannelTreeMethods(t *testing.T) {
	// 创建测试树结构
	tree := &Tree{
		CurrentID: 1,
		Name:      "根节点",
		Children: []*Node{
			{
				CurrentID: 2,
				Name:      "子节点1",
				Children: []*Node{
					{CurrentID: 4, Name: "孙节点1"},
					{CurrentID: 5, Name: "孙节点2"},
				},
			},
			{
				CurrentID: 3,
				Name:      "子节点2",
				Children: []*Node{
					{CurrentID: 6, Name: "孙节点3"},
				},
			},
		},
	}

	// 测试 GetAllDescendantIDs 方法
	ids := tree.GetAllDescendantIDs()
	expectedIDs := []uint64{1, 2, 4, 5, 3, 6}

	if len(ids) != len(expectedIDs) {
		t.Errorf("期望获取%d个ID，实际获取%d个", len(expectedIDs), len(ids))
	}

	// 验证所有ID都存在
	idMap := make(map[uint64]bool)
	for _, id := range ids {
		idMap[id] = true
	}

	for _, expectedID := range expectedIDs {
		if !idMap[expectedID] {
			t.Errorf("缺少期望的ID: %d", expectedID)
		}
	}

	t.Log("树方法测试通过")
}
