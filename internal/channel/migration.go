package channel

import (
	"fmt"
	"strconv"
	"time"

	"git.uozi.org/uozi/awm-api/model"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// MigrateToClosureTable 将现有的邻接列表数据迁移到闭包表结构
func MigrateToClosureTable() error {
	db := cosy.UseDB()

	return db.Transaction(func(tx *gorm.DB) error {
		// 1. 备份现有数据
		if err := backupOldChannelData(tx); err != nil {
			return fmt.Errorf("备份数据失败: %w", err)
		}

		// 2. 清空新表
		if err := tx.Exec("DELETE FROM channels").Error; err != nil {
			return fmt.Errorf("清空channels表失败: %w", err)
		}
		if err := tx.Exec("DELETE FROM channel_closures").Error; err != nil {
			return fmt.Errorf("清空channel_closures表失败: %w", err)
		}

		// 3. 迁移数据
		if err := migrateChannelData(tx); err != nil {
			return fmt.Errorf("迁移数据失败: %w", err)
		}

		return nil
	})
}

// backupOldChannelData 备份旧的channel数据
func backupOldChannelData(tx *gorm.DB) error {
	// 创建备份表
	backupSQL := `
		CREATE TABLE IF NOT EXISTS channels_backup_` + getCurrentTimestamp() + ` AS
		SELECT * FROM channels
	`
	return tx.Exec(backupSQL).Error
}

// getCurrentTimestamp 获取当前时间戳字符串
func getCurrentTimestamp() string {
	return strconv.FormatInt(time.Now().Unix(), 10)
}

// migrateChannelData 迁移channel数据到新结构
func migrateChannelData(tx *gorm.DB) error {
	// 查询所有旧的channel记录
	var oldChannels []struct {
		ID           uint64 `gorm:"column:id"`
		CreatedAt    uint64 `gorm:"column:created_at"`
		UpdatedAt    uint64 `gorm:"column:updated_at"`
		DeletedAt    uint   `gorm:"column:deleted_at"`
		RootUserID   uint64 `gorm:"column:root_user_id"`
		UserID       uint64 `gorm:"column:user_id"`
		RelateUserID uint64 `gorm:"column:relate_user_id"`
	}

	// 从备份表查询数据
	backupTableName := "channels_backup_" + getCurrentTimestamp()
	if err := tx.Table(backupTableName).Find(&oldChannels).Error; err != nil {
		return fmt.Errorf("查询备份数据失败: %w", err)
	}

	// 构建树结构映射
	treeMap := make(map[uint64][]uint64) // parent_id -> []child_ids
	nodeInfo := make(map[uint64]struct {
		ID        uint64
		CreatedAt uint64
		UpdatedAt uint64
		DeletedAt uint
		RootID    uint64
	})

	// 分析数据结构
	for _, old := range oldChannels {
		// 记录节点信息
		if old.UserID > 0 {
			nodeInfo[old.UserID] = struct {
				ID        uint64
				CreatedAt uint64
				UpdatedAt uint64
				DeletedAt uint
				RootID    uint64
			}{
				ID:        old.ID,
				CreatedAt: old.CreatedAt,
				UpdatedAt: old.UpdatedAt,
				DeletedAt: old.DeletedAt,
				RootID:    old.RootUserID,
			}
		}

		// 构建父子关系
		if old.RelateUserID > 0 {
			treeMap[old.UserID] = append(treeMap[old.UserID], old.RelateUserID)
		}
	}

	// 找到所有根节点
	allNodes := make(map[uint64]bool)
	childNodes := make(map[uint64]bool)

	for parentID, children := range treeMap {
		allNodes[parentID] = true
		for _, childID := range children {
			allNodes[childID] = true
			childNodes[childID] = true
		}
	}

	// 根节点是那些不是任何节点的子节点的节点
	var rootNodes []uint64
	for nodeID := range allNodes {
		if !childNodes[nodeID] {
			rootNodes = append(rootNodes, nodeID)
		}
	}

	// 为每个根节点构建完整的树
	for _, rootID := range rootNodes {
		if err := buildTreeFromRoot(tx, rootID, treeMap, nodeInfo); err != nil {
			return fmt.Errorf("构建树失败 (根节点 %d): %w", rootID, err)
		}
	}

	return nil
}

// buildTreeFromRoot 从根节点开始构建整个树
func buildTreeFromRoot(tx *gorm.DB, rootID uint64, treeMap map[uint64][]uint64, nodeInfo map[uint64]struct {
	ID        uint64
	CreatedAt uint64
	UpdatedAt uint64
	DeletedAt uint
	RootID    uint64
}) error {
	closureService := &ClosureService{db: tx}

	// 递归构建树
	visited := make(map[uint64]bool)
	return buildNodeRecursively(tx, rootID, 0, rootID, strconv.FormatUint(rootID, 10), 0, treeMap, nodeInfo, visited, closureService)
}

// buildNodeRecursively 递归构建节点
func buildNodeRecursively(
	tx *gorm.DB,
	nodeID uint64,
	parentID uint64,
	treeRootID uint64,
	nodePath string,
	level uint,
	treeMap map[uint64][]uint64,
	nodeInfo map[uint64]struct {
		ID        uint64
		CreatedAt uint64
		UpdatedAt uint64
		DeletedAt uint
		RootID    uint64
	},
	visited map[uint64]bool,
	closureService *ClosureService,
) error {
	// 防止循环引用
	if visited[nodeID] {
		return nil
	}
	visited[nodeID] = true

	// 获取节点信息
	info, exists := nodeInfo[nodeID]
	if !exists {
		// 如果节点信息不存在，创建默认信息
		info = struct {
			ID        uint64
			CreatedAt uint64
			UpdatedAt uint64
			DeletedAt uint
			RootID    uint64
		}{
			ID:        0,
			CreatedAt: uint64(time.Now().Unix()),
			UpdatedAt: uint64(time.Now().Unix()),
			DeletedAt: 0,
			RootID:    treeRootID,
		}
	}

	// 创建新的channel记录
	channel := &model.Channel{
		Model: model.Model{
			ID:        info.ID,
			CreatedAt: info.CreatedAt,
			UpdatedAt: info.UpdatedAt,
			DeletedAt: 0, // 重置删除状态
		},
		NodeUserID:     nodeID,
		ParentUserID:   parentID,
		TreeRootUserID: treeRootID,
		Level:          level,
		NodePath:       nodePath,
	}

	if err := tx.Create(channel).Error; err != nil {
		return fmt.Errorf("创建节点失败 (节点 %d): %w", nodeID, err)
	}

	// 创建闭包表记录
	// 1. 自引用记录
	selfClosure := &model.ChannelClosure{
		AncestorUserID:   nodeID,
		DescendantUserID: nodeID,
		PathLength:       0,
		TreeRootUserID:   treeRootID,
	}
	if err := tx.Create(selfClosure).Error; err != nil {
		return fmt.Errorf("创建自引用闭包记录失败 (节点 %d): %w", nodeID, err)
	}

	// 2. 如果有父节点，创建祖先关系
	if parentID > 0 {
		// 查询父节点的所有祖先
		var ancestorClosures []model.ChannelClosure
		if err := tx.Where("descendant_user_id = ? AND tree_root_user_id = ?",
			parentID, treeRootID).Find(&ancestorClosures).Error; err != nil {
			return fmt.Errorf("查询祖先节点失败 (节点 %d): %w", nodeID, err)
		}

		// 为每个祖先创建到当前节点的闭包记录
		for _, ancestorClosure := range ancestorClosures {
			newClosure := &model.ChannelClosure{
				AncestorUserID:   ancestorClosure.AncestorUserID,
				DescendantUserID: nodeID,
				PathLength:       ancestorClosure.PathLength + 1,
				TreeRootUserID:   treeRootID,
			}
			if err := tx.Create(newClosure).Error; err != nil {
				return fmt.Errorf("创建祖先闭包记录失败 (节点 %d): %w", nodeID, err)
			}
		}
	}

	// 递归处理子节点
	children := treeMap[nodeID]
	for _, childID := range children {
		childPath := nodePath + "/" + strconv.FormatUint(childID, 10)
		if err := buildNodeRecursively(
			tx, childID, nodeID, treeRootID, childPath, level+1,
			treeMap, nodeInfo, visited, closureService,
		); err != nil {
			return err
		}
	}

	return nil
}

// VerifyMigration 验证迁移结果
func VerifyMigration() error {
	db := cosy.UseDB()

	// 检查数据一致性
	var channelCount int64
	if err := db.Model(&model.Channel{}).Count(&channelCount).Error; err != nil {
		return fmt.Errorf("统计channel记录失败: %w", err)
	}

	var closureCount int64
	if err := db.Model(&model.ChannelClosure{}).Count(&closureCount).Error; err != nil {
		return fmt.Errorf("统计closure记录失败: %w", err)
	}

	fmt.Printf("迁移完成: %d 个节点, %d 个闭包关系\n", channelCount, closureCount)

	// 验证每个节点都有自引用记录
	var selfRefCount int64
	if err := db.Model(&model.ChannelClosure{}).
		Where("ancestor_user_id = descendant_user_id").
		Count(&selfRefCount).Error; err != nil {
		return fmt.Errorf("统计自引用记录失败: %w", err)
	}

	if selfRefCount != channelCount {
		return fmt.Errorf("自引用记录数量不匹配: 期望 %d, 实际 %d", channelCount, selfRefCount)
	}

	fmt.Println("数据验证通过")
	return nil
}
