package view

import "gorm.io/gorm"

const ChannelView = "channels_view"

// createChannelView 创建渠道视图, 基于新的闭包表结构
func createChannelView(db *gorm.DB) (err error) {
	sql := `
        SELECT
			u.id AS user_id,
			u.name,
			u.phone,
			u.email,
			u.status,
			u.assessment_protection_end_at,
			u.channel_type,
			u.deleted_at AS user_deleted_at,
			u.created_at,
			u.updated_at,
			COALESCE(c.id, 0) AS id,
			COALESCE(c.node_user_id, u.id) AS node_user_id,
			COALESCE(c.parent_user_id, 0) AS parent_user_id,
			COALESCE(c.tree_root_user_id, u.id) AS tree_root_user_id,
			COALESCE(c.level, 0) AS level,
			COALESCE(c.node_path, CAST(u.id AS CHAR)) AS node_path,
			COALESCE(c.deleted_at, 0) AS deleted_at
        FROM
            users u
        LEFT JOIN
            channels c ON u.id = c.node_user_id
    `

	return db.Migrator().CreateView(ChannelView, gorm.ViewOption{Replace: true, Query: db.Raw(sql)})
}
