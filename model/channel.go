package model

type Channel struct {
	Model
	RootUserID   uint64 `json:"root_user_id,string,omitempty" gorm:"index;comment:根用户ID"`
	RootUser     *User  `json:"root_user,omitempty"`
	UserID       uint64 `json:"user_id,string,omitempty" gorm:"index;comment:自身用户ID"`
	User         *User  `json:"user,omitempty"`
	RelateUserID uint64 `json:"relate_user_id,string,omitempty" gorm:"index;comment:下级用户ID"`
	RelateUser   *User  `json:"relate_user,omitempty"`
}

type ChannelView struct {
	Model
	Channel
	Name         string `json:"name,omitempty"`
	Phone        string `json:"phone,omitempty"`
	Email        string `json:"email,omitempty"`
	Status       string `json:"status,omitempty"`
	ParentUserID uint64 `json:"parent_user_id,string,omitempty"`
	ParentUser   *User  `json:"parent_user,omitempty"`
}
