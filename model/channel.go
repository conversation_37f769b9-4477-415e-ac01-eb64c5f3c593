package model

// Channel 渠道节点表 - 存储渠道节点的基本信息
type Channel struct {
	Model
	// 节点用户ID - 该渠道节点对应的用户ID
	NodeUserID uint64 `json:"node_user_id,string,omitempty" gorm:"uniqueIndex;comment:节点用户ID"`
	NodeUser   *User  `json:"node_user,omitempty"`
	// 父节点用户ID - 该节点的直接上级用户ID，根节点为0
	ParentUserID uint64 `json:"parent_user_id,string,omitempty" gorm:"index;comment:父节点用户ID"`
	ParentUser   *User  `json:"parent_user,omitempty"`
	// 树根用户ID - 该节点所属树的根用户ID
	TreeRootUserID uint64 `json:"tree_root_user_id,string,omitempty" gorm:"index;comment:树根用户ID"`
	TreeRootUser   *User  `json:"tree_root_user,omitempty"`
	// 节点层级 - 从根节点开始的层级，根节点为0
	Level uint `json:"level" gorm:"index;comment:节点层级"`
	// 节点路径 - 从根节点到当前节点的路径，用/分隔
	NodePath string `json:"node_path" gorm:"index;comment:节点路径"`
}

// ChannelClosure 渠道闭包表 - 存储所有祖先-后代关系
type ChannelClosure struct {
	Model
	// 祖先节点用户ID
	AncestorUserID uint64 `json:"ancestor_user_id,string,omitempty" gorm:"index:idx_ancestor_descendant,priority:1;comment:祖先节点用户ID"`
	AncestorUser   *User  `json:"ancestor_user,omitempty"`
	// 后代节点用户ID
	DescendantUserID uint64 `json:"descendant_user_id,string,omitempty" gorm:"index:idx_ancestor_descendant,priority:2;index:idx_descendant;comment:后代节点用户ID"`
	DescendantUser   *User  `json:"descendant_user,omitempty"`
	// 路径长度 - 祖先到后代的距离，自己到自己为0
	PathLength uint `json:"path_length" gorm:"index;comment:路径长度"`
	// 树根用户ID - 用于快速过滤不同的树
	TreeRootUserID uint64 `json:"tree_root_user_id,string,omitempty" gorm:"index;comment:树根用户ID"`
}

// ChannelView 渠道视图 - 用于列表展示的视图结构
type ChannelView struct {
	Model
	Channel
	Name         string `json:"name,omitempty"`
	Phone        string `json:"phone,omitempty"`
	Email        string `json:"email,omitempty"`
	Status       string `json:"status,omitempty"`
	ParentUserID uint64 `json:"parent_user_id,string,omitempty"`
	ParentUser   *User  `json:"parent_user,omitempty"`
}
