package examples

import (
	"fmt"
	"log"

	"git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
	mysql "github.com/uozi-tech/cosy-driver-mysql"
	"github.com/uozi-tech/cosy/settings"
)

// ChannelClosureUsageExample 展示如何使用新的闭包表服务
func ChannelClosureUsageExample() {
	// 初始化数据库连接
	settings.Init("../app.ini")
	cosy.RegisterModels(model.GenerateAllModel()...)
	db := cosy.InitDB(mysql.Open(settings.DataBaseSettings))
	query.Init(db)
	model.Use(db)

	// 创建闭包表服务实例
	closureService := channel.NewClosureService()

	// 示例1: 创建树形结构
	fmt.Println("=== 示例1: 创建树形结构 ===")
	
	// 创建根节点 (用户ID: 1000)
	if err := closureService.CreateNode(1000, 0); err != nil {
		log.Printf("创建根节点失败: %v", err)
		return
	}
	fmt.Println("✓ 创建根节点 1000")

	// 创建子节点
	if err := closureService.CreateNode(1001, 1000); err != nil {
		log.Printf("创建子节点失败: %v", err)
		return
	}
	fmt.Println("✓ 创建子节点 1001 (父节点: 1000)")

	if err := closureService.CreateNode(1002, 1000); err != nil {
		log.Printf("创建子节点失败: %v", err)
		return
	}
	fmt.Println("✓ 创建子节点 1002 (父节点: 1000)")

	// 创建孙节点
	if err := closureService.CreateNode(1003, 1001); err != nil {
		log.Printf("创建孙节点失败: %v", err)
		return
	}
	fmt.Println("✓ 创建孙节点 1003 (父节点: 1001)")

	// 示例2: 查询操作
	fmt.Println("\n=== 示例2: 查询操作 ===")

	// 获取直接子节点
	children, err := closureService.GetChildren(1000)
	if err != nil {
		log.Printf("获取子节点失败: %v", err)
		return
	}
	fmt.Printf("✓ 节点 1000 的直接子节点数量: %d\n", len(children))
	for _, child := range children {
		fmt.Printf("  - 子节点: %d (层级: %d)\n", child.NodeUserID, child.Level)
	}

	// 获取所有后代节点
	descendants, err := closureService.GetAllDescendants(1000)
	if err != nil {
		log.Printf("获取后代节点失败: %v", err)
		return
	}
	fmt.Printf("✓ 节点 1000 的所有后代节点数量: %d\n", len(descendants))
	for _, descendant := range descendants {
		fmt.Printf("  - 后代节点: %d (层级: %d, 路径: %s)\n", 
			descendant.NodeUserID, descendant.Level, descendant.NodePath)
	}

	// 获取祖先节点
	ancestors, err := closureService.GetAncestors(1003)
	if err != nil {
		log.Printf("获取祖先节点失败: %v", err)
		return
	}
	fmt.Printf("✓ 节点 1003 的祖先节点数量: %d\n", len(ancestors))
	for _, ancestor := range ancestors {
		fmt.Printf("  - 祖先节点: %d (层级: %d)\n", ancestor.NodeUserID, ancestor.Level)
	}

	// 示例3: 获取完整树结构
	fmt.Println("\n=== 示例3: 获取完整树结构 ===")
	
	tree, err := closureService.GetTree(1000)
	if err != nil {
		log.Printf("获取树结构失败: %v", err)
		return
	}
	fmt.Println("✓ 完整树结构:")
	printTree(tree, 0)

	// 示例4: 节点移动
	fmt.Println("\n=== 示例4: 节点移动 ===")
	
	// 将节点1003从1001移动到1002下
	if err := closureService.MoveNode(1003, 1002); err != nil {
		log.Printf("移动节点失败: %v", err)
		return
	}
	fmt.Println("✓ 将节点 1003 从 1001 移动到 1002 下")

	// 查看移动后的树结构
	tree, err = closureService.GetTree(1000)
	if err != nil {
		log.Printf("获取树结构失败: %v", err)
		return
	}
	fmt.Println("✓ 移动后的树结构:")
	printTree(tree, 0)

	// 示例5: 使用传统API (向后兼容)
	fmt.Println("\n=== 示例5: 使用传统API ===")
	
	// 使用传统的GetChannelTree方法
	legacyTree, err := channel.GetChannelTree(1000)
	if err != nil {
		log.Printf("获取传统树结构失败: %v", err)
		return
	}
	fmt.Println("✓ 传统API获取的树结构:")
	printLegacyTree((*channel.Node)(legacyTree), 0)

	// 使用传统的GetChannelChildren方法
	legacyChildren, err := channel.GetChannelChildren(1001)
	if err != nil {
		log.Printf("获取传统子树失败: %v", err)
		return
	}
	fmt.Println("✓ 传统API获取的子树结构:")
	printLegacyTree((*channel.Node)(legacyChildren), 0)

	// 示例6: 节点删除
	fmt.Println("\n=== 示例6: 节点删除 ===")
	
	// 删除中间节点，子节点会自动重新连接到父节点
	if err := closureService.DeleteNode(1001); err != nil {
		log.Printf("删除节点失败: %v", err)
		return
	}
	fmt.Println("✓ 删除节点 1001 (子节点会自动重新连接)")

	// 查看删除后的树结构
	tree, err = closureService.GetTree(1000)
	if err != nil {
		log.Printf("获取树结构失败: %v", err)
		return
	}
	fmt.Println("✓ 删除后的树结构:")
	printTree(tree, 0)

	fmt.Println("\n=== 示例完成 ===")
}

// printTree 打印树结构
func printTree(node *channel.Node, depth int) {
	if node == nil {
		return
	}

	// 打印缩进
	for i := 0; i < depth; i++ {
		fmt.Print("  ")
	}
	
	fmt.Printf("├─ %s (ID: %d)\n", node.Name, node.CurrentID)
	
	// 递归打印子节点
	for _, child := range node.Children {
		printTree(child, depth+1)
	}
}

// printLegacyTree 打印传统树结构
func printLegacyTree(node *channel.Node, depth int) {
	if node == nil {
		return
	}

	// 打印缩进
	for i := 0; i < depth; i++ {
		fmt.Print("  ")
	}
	
	fmt.Printf("├─ %s (ID: %d, Root: %d)\n", node.Name, node.CurrentID, node.RootID)
	
	// 递归打印子节点
	for _, child := range node.Children {
		printLegacyTree(child, depth+1)
	}
}

// PerformanceComparisonExample 性能对比示例
func PerformanceComparisonExample() {
	fmt.Println("=== 性能对比示例 ===")
	
	// 这里可以添加性能测试代码
	// 比较闭包表查询和传统递归查询的性能差异
	
	fmt.Println("注意: 在实际使用中，闭包表查询的性能优势会随着树的深度和节点数量的增加而更加明显")
	fmt.Println("- 查询子树: O(1) vs O(n)")
	fmt.Println("- 查询祖先: O(1) vs O(n)")  
	fmt.Println("- 查询路径: O(1) vs O(n)")
	fmt.Println("- 统计后代: O(1) vs O(n)")
}

// CleanupExample 清理示例数据
func CleanupExample() {
	fmt.Println("=== 清理示例数据 ===")
	
	db := cosy.UseDB()
	
	// 删除示例数据
	db.Exec("DELETE FROM channel_closures WHERE tree_root_user_id = 1000")
	db.Exec("DELETE FROM channels WHERE tree_root_user_id = 1000")
	
	fmt.Println("✓ 示例数据已清理")
}
