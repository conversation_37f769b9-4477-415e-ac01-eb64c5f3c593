<script setup lang="ts">
import type { ChannelCommissionT, ChannelNode } from '~/api/channel'
import { PlusOutlined } from '@ant-design/icons-vue'
import { StdForm, StdSelector } from '@uozi-admin/curd'
import { type FormInstance, message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { h } from 'vue'
import { Vue3TreeOrg } from 'vue3-tree-org'
import { channelApi, channelCommissionApi } from '~/api/channel'
import { type User, userApi } from '~/api/user'
import { ChannelStrategyStatus, ChannelStrategyStatusMap, UserChannelType } from '~/constants'
import { PATH_CHANNEL_COMMISSION_POLICY, PATH_CHANNEL_COMMISSION_TABLE } from '~/router'
import { channelColumns } from '../channelColumns'
import 'vue3-tree-org/lib/vue3-tree-org.css'

const props = defineProps<{
  channelId?: string
  privileged?: boolean
}>()

const editorVisible = defineModel<boolean>('open', { default: false })

const route = useRoute()
const router = useRouter()

// 合并路由查询参数和UI状态
const channelId = computed(() => route.query.channel_id as string | undefined)

// 优化路由参数监听，避免重复请求
const isChannelDataLoaded = ref(false)

// 统一管理UI状态
const uiState = reactive({
  selectUserWaysVisible: false,
  userFormVisible: false,
  channelSelectorVisible: false,
  channelStrategyModalOpen: false,
  datePickerVisible: false,
})

// 统一管理数据状态
const dataState = reactive({
  channelData: {} as ChannelNode,
  selectedNode: {} as ChannelNode,
  channelStrategyList: [] as ChannelCommissionT[],
  currentChannelStrategy: undefined as Partial<ChannelCommissionT> | undefined,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
  },
  selectedDate: undefined as string | undefined,
})

const stdFormRef = ref<{ formRef: FormInstance }>()

watch(() => route.query, async (newQuery, oldQuery) => {
  // 处理渠道编辑窗口
  if (channelId.value) {
    editorVisible.value = true

    // 只有在channelId改变或首次加载时才重新请求数据
    if (newQuery.channel_id === oldQuery?.channel_id || isChannelDataLoaded.value) {
      return
    }

    isChannelDataLoaded.value = true
    await loadChannelTree()

    if (props.privileged) {
      await loadChannelStrategyList()
    }
  }
  else {
    editorVisible.value = false
    isChannelDataLoaded.value = false
  }
}, { immediate: true, deep: true })

function handleNodeSelect(node: ChannelNode) {
  dataState.selectedNode = node
  loadChannelStrategyList()
}

async function loadChannelStrategyList() {
  if (!props.privileged) {
    return
  }
  try {
    const response = await channelCommissionApi.getList({
      channel_id: dataState.selectedNode.current_id,
      page: dataState.pagination.page,
      page_size: dataState.pagination.pageSize,
      privileged: props.privileged,
    })
    dataState.channelStrategyList = response.data
    dataState.pagination.total = response.pagination.total
  }
  catch {
    message.error($gettext('Failed to load strategy list'))
  }
}

// 渠道树加载函数
async function loadChannelTree() {
  try {
    const response = await channelApi.getChannel({
      user_id: props.channelId!,
      privileged: props.privileged,
    })
    dataState.channelData = response
    walkAndSelectNode(response)
  }
  catch {
    message.error($gettext('Failed to load channel data'))
  }
}

// 树节点查找
function walkAndSelectNode(node: ChannelNode) {
  if (node.current_id === props.channelId) {
    dataState.selectedNode = node
    return
  }

  if (node.children) {
    for (const child of node.children) {
      walkAndSelectNode(child)
    }
  }
}

async function handleChannelStrategyModalSave() {
  stdFormRef.value?.formRef?.validateFields().then(async () => {
    try {
      const strategy = dataState.currentChannelStrategy!
      if (strategy.id) {
        await channelCommissionApi.updateItem(strategy.id, strategy, {
          params: {
            privileged: props.privileged,
          },
        })
      }
      else {
        await channelCommissionApi.createItem(strategy, {
          params: {
            privileged: props.privileged,
          },
        })
      }
      handleChannelStrategyModalClose()
      loadChannelStrategyList()
      message.success($gettext('Strategy saved successfully'))
    }
    catch {
      message.error($gettext('Save failed'))
    }
  })
}

async function handleChannelStrategyDelete(record: ChannelCommissionT) {
  try {
    await channelCommissionApi.deleteItem(record.id)
    await loadChannelStrategyList()
    message.success($gettext('Strategy deleted successfully'))
  }
  catch {
    message.error($gettext('Delete failed'))
  }
}

function handleChannelCommissionPolicyOpen(record?: ChannelCommissionT) {
  if (!props.privileged) {
    message.error($gettext('You are not authorized to view team commission policy'))
    return
  }

  router.push({
    path: PATH_CHANNEL_COMMISSION_POLICY,
    query: {
      channel_id: channelId.value,
      channel_commission_id: record?.id,
      from: route.fullPath,
    },
  })
}

async function handleSubordinateAdd(record: User[]) {
  if (!props.privileged) {
    message.error($gettext('You are not authorized to add subordinate'))
    return
  }

  if (!record.length || !dataState.selectedNode.current_id)
    return
  console.log(record)
  try {
    await channelApi.createItem({
      root_user_id: dataState.selectedNode.root_id,
      user_id: dataState.selectedNode.current_id,
      relate_user_id: record[0].id,
    })
    await loadChannelTree()
    message.success($gettext('Subordinate added successfully'))
  }
  catch {
    message.error($gettext('Add subordinate failed'))
  }
}

async function handleSubordinateDelete() {
  if (!props.privileged) {
    message.error($gettext('You are not authorized to delete subordinate'))
    return
  }

  try {
    await channelApi.removeRelation({
      root_user_id: dataState.selectedNode.root_id,
      relate_user_id: dataState.selectedNode.current_id,
    })
    await loadChannelTree()
    message.success($gettext('Subordinate deleted successfully'))
  }
  catch {
    message.error($gettext('Delete subordinate failed'))
  }
}

// 策略相关处理函数
function handleChannelCommissionTableOpen() {
  router.push({
    path: PATH_CHANNEL_COMMISSION_TABLE,
    query: {
      channel_id: channelId.value,
      channel_name: dataState.selectedNode.name,
      date: dayjs().unix().toString(),
      from: route.path,
    },
  })
}

function handleChannelStrategyModalOpen(record?: ChannelCommissionT) {
  dataState.currentChannelStrategy = {
    channel_id: dataState.selectedNode.current_id,
    ...record,
  }
  uiState.channelStrategyModalOpen = true
}

function handleDatePickerOpenChange(open: boolean) {
  uiState.datePickerVisible = open
}

function handleChannelStrategyModalClose() {
  stdFormRef.value?.formRef?.resetFields()
  uiState.channelStrategyModalOpen = false
}

function handleClose() {
  editorVisible.value = false
  router.push({
    query: {
      channel_id: undefined,
    },
  })
}
</script>

<template>
  <AModal
    v-model:open="editorVisible"
    :title="$gettext('Team')"
    centered
    width="80vw"
    class="max-w-1000px min-w-360px"
    :body-style="{
      height: '60vh',
      overflow: 'auto',
    }"
    :footer="false"
    @cancel="handleClose"
  >
    <AFlex
      class="h-full of-hidden <md:(flex-col !of-auto)"
      :gap="24"
    >
      <AFlex
        vertical
        class="md:of-hidden w-full h-full"
        :class="{
          'md:w-60%': privileged,
        }"
      >
        <Vue3TreeOrg
          style="height: 500px"
          class="!bg-transparent"
          :data="dataState.channelData"
          center
          :tool-bar="{
            scale: true, restore: true, expand: false, zoom: true, fullscreen: false,
          }"
        >
          <template #default="{ node }">
            <div
              class="pa-4 rounded-md cursor-pointer"
              :class="{
                'bg-blue-600 text-white':
                  node.$$data.current_id === dataState.selectedNode?.current_id,
              }"
              @click="handleNodeSelect(node.$$data)"
            >
              <div class="text-center font-weight-medium">
                {{ node?.$$data?.name }}
              </div>
            </div>
          </template>
        </Vue3TreeOrg>
        <AFlex
          v-if="dataState.selectedNode.current_id"
          class="w-full flex-1 p-4"
          :class="{
            'justify-end': !privileged,
          }"
          gap="12"
          align="end"
          wrap="wrap"
        >
          <AButton
            v-if="privileged"
            type="primary"
            @click="uiState.selectUserWaysVisible = true"
          >
            {{ $pgettext('为 %{name} 添加下属', 'Add Subordinate For %{name}', { name: dataState.selectedNode?.name }) }}
          </AButton>
          <APopconfirm
            v-if="privileged && (dataState.selectedNode.current_id !== dataState.selectedNode.root_id || !dataState.selectedNode.root_id)"
            :title="$pgettext('确定删除下级吗？', 'Are you sure to delete the subordinate?')"
            @confirm="handleSubordinateDelete"
          >
            <AButton
              danger
              type="primary"
            >
              {{ $pgettext('删除 %{name}', 'Delete %{name}', { name: dataState.selectedNode?.name }) }}
            </AButton>
          </APopconfirm>
          <AModal
            v-model:open="uiState.datePickerVisible"
            :footer="false"
            centered
            :title="$gettext('Reference Time')"
          >
            <div class="h-350px">
              <ADatePicker
                v-model:value="dataState.selectedDate"
                show-time
                popup-class-name="reference-time-picker"
                :get-popup-container="triggerNode => triggerNode?.parentNode as HTMLElement"
                :open="uiState.datePickerVisible"
                @ok="handleChannelCommissionTableOpen"
                @open-change="handleDatePickerOpenChange"
              />
            </div>
          </AModal>
          <AButton
            type="primary"
            @click="uiState.datePickerVisible = true"
          >
            {{ $pgettext('查看团队佣金', 'View Team Commission') }}
          </AButton>
        </AFlex>
      </AFlex>
      <AFlex
        v-if="privileged"
        vertical
        class="h-full flex-1"
      >
        <AFlex
          justify="space-between"
          align="center"
        >
          <span class="text-18px font-semibold">
            {{ $pgettext('策略', 'Policy') }}
          </span>
          <AButton
            :icon="h(PlusOutlined)"
            type="link"
            @click="handleChannelStrategyModalOpen()"
          >
            {{ $pgettext('新增策略', 'Create Strategy') }}
          </AButton>
        </AFlex>
        <ATimeline
          v-if="dataState.channelStrategyList.length > 0"
          class="flex-1 of-auto w-full py-4"
        >
          <ATimelineItem
            v-for="record in dataState.channelStrategyList"
            :key="record.id"
            :color="record.status === ChannelStrategyStatus.Published ? 'blue' : 'gray'"
          >
            <div class="pb-2">
              {{ dayjs.unix(record.effected_at).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
            <ACard class="bg-truegray-1 dark:bg-truegray-8">
              <AFlex
                vertical
                gap="8"
              >
                <AFlex justify="space-between">
                  <label class="text-truegray">{{ $gettext('Remark') }}</label>
                  <span>
                    {{ record.remark || $gettext('None') }}
                  </span>
                </AFlex>
                <AFlex justify="space-between">
                  <label class="text-truegray">{{ $gettext('Status') }}</label>
                  <ATag
                    :color="record.status === ChannelStrategyStatus.Published ? 'blue' : 'gray'"
                    class="m-0"
                  >
                    {{ ChannelStrategyStatusMap[record.status]?.() }}
                  </ATag>
                </AFlex>
                <AFlex justify="space-between">
                  <label class="text-truegray">{{ $gettext('Actions') }}</label>
                  <AFlex
                    class="w-60% <md:(flex-col items-end)"
                    gap="12"
                    justify="end"
                    wrap="wrap"
                  >
                    <AButton
                      size="small"
                      type="link"
                      class="!px-0"
                      @click="handleChannelStrategyModalOpen(record)"
                    >
                      {{ $pgettext('修改元数据', 'Edit Metadata') }}
                    </AButton>
                    <AButton
                      size="small"
                      type="link"
                      class="!px-0"
                      @click="handleChannelCommissionPolicyOpen(record)"
                    >
                      {{ $gettext('View') }}
                    </AButton>
                    <APopconfirm
                      :title="$pgettext('确定删除策略吗？', 'Are you sure to delete the strategy?')"
                      @confirm="handleChannelStrategyDelete(record)"
                    >
                      <AButton
                        size="small"
                        type="link"
                        class="!px-0"
                        danger
                      >
                        {{ $gettext('Delete') }}
                      </AButton>
                    </APopconfirm>
                  </AFlex>
                </AFlex>
              </AFlex>
            </ACard>
          </ATimelineItem>
        </ATimeline>
        <AEmpty
          v-else-if="!dataState.channelStrategyList.length"
          class="h-full flex flex-col items-center justify-center pb-12"
          :description="$gettext('No data')"
        />
        <APagination
          v-model:current="dataState.pagination.page"
          v-model:page-size="dataState.pagination.pageSize"
          class="flex justify-end pt-4"
          :total="dataState.pagination.total"
          show-size-changer
        />
      </AFlex>
    </AFlex>
    <StdSelector
      v-model:visible="uiState.channelSelectorVisible"
      hide-input-container
      :modal-props="{
        mask: true,
        centered: true,
      }"
      :overwrite-params="{ privileged: true }"
      :get-list-api="() => userApi.getPublicUsers({ channel_type: UserChannelType.Normal })"
      :columns="channelColumns"
      @selected-records="handleSubordinateAdd"
    />
  </AModal>

  <AModal
    v-model:open="uiState.channelStrategyModalOpen"
    :title="$gettext('Team Strategy')"
    centered
    :width="400"
    :ok-text="$gettext('Save')"
    :cancel-text="$gettext('Close')"
    destroy-on-close
    @ok="handleChannelStrategyModalSave"
    @cancel="handleChannelStrategyModalClose"
  >
    <StdForm
      ref="stdFormRef"
      v-model:data="dataState.currentChannelStrategy"
      :columns="[
        {
          title: () => $gettext('Remark'),
          dataIndex: 'remark',
          edit: {
            type: 'textarea',
            textarea: {
              rows: 6,
            },
          },
        },
        {
          title: () => $gettext('Effected At'),
          dataIndex: 'effected_at',
          edit: {
            type: 'datetime',
            datetime: {
              class: '!w-60%',
            },
            formItem: {
              required: true,
            },
          },
        },
        {
          title: () => $gettext('Status'),
          dataIndex: 'status',
          edit: {
            type: 'select',
            select: {
              class: '!w-60%',
              mask: ChannelStrategyStatusMap,
            },
            formItem: {
              required: true,
            },
          },
        },
      ]"
    />
  </AModal>

  <AModal
    v-model:open="uiState.selectUserWaysVisible"
    :title="$gettext('Select User From')"
    centered
    width="240px"
    :footer="false"
  >
    <div class="flex flex-col gap-4 py-4">
      <AButton @click="(uiState.channelSelectorVisible = true) && (uiState.selectUserWaysVisible = false)">
        {{ $gettext('Existed Users') }}
      </AButton>
      <AButton
        type="primary"
        @click="(uiState.userFormVisible = true) && (uiState.selectUserWaysVisible = false)"
      >
        {{ $gettext('New Users') }}
      </AButton>
    </div>
  </AModal>

  <UserForm
    v-model:visible="uiState.userFormVisible"
    :channel-type="UserChannelType.Normal"
    @save="(user) => handleSubordinateAdd([user])"
  />
</template>

<style scoped>
:deep(.tree-org-node__inner) {
  @apply rounded-md;
}

:deep(.zoom-container) {
  @apply pa-4;
}
</style>
