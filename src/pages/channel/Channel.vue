<script setup lang="ts">
import type { Channel } from '~/api/channel'
import { StdCurd } from '@uozi-admin/curd'
import { isArray } from 'lodash-es'
import { channelApi } from '~/api/channel'
import { usePermissionStore, useUserStore } from '~/store'
import { channelColumns } from './channelColumns'
import ChannelEditor from './components/ChannelEditor.vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const channelId = computed(() => route.query.channel_id as string | undefined)

function handleEdit(record: Channel) {
  router.push({
    query: {
      ...route.query,
      // 优先使用新的字段，向后兼容旧字段
      channel_id: record.node_user_id || record.user_id,
    },
  })
}

const privileged = computed(() => route.meta?.privileged)

const columns = computed(() => {
  if (privileged.value) {
    return channelColumns
  }
  return channelColumns.filter(column =>
    column.dataIndex !== 'actions'
    && isArray(column.dataIndex)
    && column.dataIndex.join('.') !== 'relate_user.name')
})

const { getActionMap } = usePermissionStore()

const actionMap = getActionMap()

function handleMyChannelTree() {
  router.push({
    query: { ...route.query, channel_id: userStore.info.id },
  })
}
</script>

<template>
  <div>
    <StdCurd
      :columns="columns"
      :api="channelApi"
      :overwrite-params="{
        privileged,
      }"
      disable-add
      disable-edit
      disable-delete
      disable-view
      disable-trash
      disable-export
    >
      <template
        v-if="!privileged"
        #beforeListActions
      >
        <AButton
          type="link"
          size="small"
          @click="handleMyChannelTree"
        >
          {{ $gettext('My Team') }}
        </AButton>
      </template>
      <template #beforeActions="{ record }">
        <AButton
          v-if="actionMap.write"
          type="link"
          size="small"
          @click="handleEdit(record)"
        >
          {{ $gettext('Edit') }}
        </AButton>
      </template>
    </StdCurd>
    <ChannelEditor
      v-if="actionMap.write"
      :privileged="privileged"
      :open="channelId !== undefined"
      :channel-id="channelId"
    />
  </div>
</template>

<style scoped>
:deep(.tree-org-node__content .tree-org-node__inner) {
  background-color: transparent;
  box-shadow: none;
}
</style>
