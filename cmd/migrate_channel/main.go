package main

import (
	"flag"
	"fmt"
	"log"

	"git.uozi.org/uozi/awm-api/internal/channel"
	"git.uozi.org/uozi/awm-api/model"
	"git.uozi.org/uozi/awm-api/query"
	"github.com/uozi-tech/cosy"
	mysql "github.com/uozi-tech/cosy-driver-mysql"
	"github.com/uozi-tech/cosy/settings"
)

func main() {
	var confPath string
	var dryRun bool
	var verify bool

	flag.StringVar(&confPath, "config", "app.ini", "配置文件路径")
	flag.BoolVar(&dryRun, "dry-run", false, "仅验证，不执行实际迁移")
	flag.BoolVar(&verify, "verify", false, "验证迁移结果")
	flag.Parse()

	// 初始化配置
	settings.Init(confPath)

	// 初始化数据库连接
	cosy.RegisterModels(model.GenerateAllModel()...)
	cosy.RegisterInitFunc(func() {
		db := cosy.InitDB(mysql.Open(settings.DataBaseSettings))
		query.Init(db)
		model.Use(db)
	})

	// 手动初始化数据库连接
	db := cosy.InitDB(mysql.Open(settings.DataBaseSettings))
	query.Init(db)
	model.Use(db)

	if verify {
		// 验证迁移结果
		fmt.Println("开始验证迁移结果...")
		if err := channel.VerifyMigration(); err != nil {
			log.Fatalf("验证失败: %v", err)
		}
		fmt.Println("验证完成")
		return
	}

	if dryRun {
		fmt.Println("这是一个干运行，不会执行实际的迁移操作")
		fmt.Println("迁移将会:")
		fmt.Println("1. 备份现有的channels表数据")
		fmt.Println("2. 清空channels和channel_closures表")
		fmt.Println("3. 将邻接列表数据转换为闭包表结构")
		fmt.Println("4. 重新插入转换后的数据")
		fmt.Println("\n要执行实际迁移，请移除 --dry-run 参数")
		return
	}

	fmt.Println("开始迁移频道数据到闭包表结构...")
	fmt.Println("警告: 这个操作将修改数据库结构，请确保已经备份数据库!")
	fmt.Print("确认继续? (y/N): ")

	var confirm string
	fmt.Scanln(&confirm)
	if confirm != "y" && confirm != "Y" {
		fmt.Println("迁移已取消")
		return
	}

	// 执行迁移
	if err := channel.MigrateToClosureTable(); err != nil {
		log.Fatalf("迁移失败: %v", err)
	}

	fmt.Println("迁移完成!")

	// 自动验证
	fmt.Println("正在验证迁移结果...")
	if err := channel.VerifyMigration(); err != nil {
		log.Fatalf("验证失败: %v", err)
	}

	fmt.Println("迁移和验证都已成功完成!")
}
